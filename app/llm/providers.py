"""
LLM Provider implementations.

This module contains provider classes for different LLM services,
including OpenAI, OpenAI-compatible services, and others.
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict, Any
from llama_index.core.llms import LLM
from llama_index.llms.openai import OpenAI
from llama_index.llms.openai_like import OpenAILike

import logging
from .llm_config import LLMProviderConfig, LLMProviderType

logger = logging.getLogger("llm.providers")


class LLMProvider(ABC):
    """Abstract base class for LLM providers."""
    
    def __init__(self, config: LLMProviderConfig):
        """Initialize provider with configuration."""
        self.config = config
        self.logger = logging.getLogger(f"llm.providers.{config.name}")
    
    @abstractmethod
    def create_llm(self, model: Optional[str] = None, **kwargs) -> LLM:
        """Create an LLM instance."""
        pass
    
    def validate_config(self) -> bool:
        """Validate provider configuration."""
        if not self.config.api_key:
            self.logger.error(f"API key not configured for provider {self.config.name}")
            return False
        return True


class OpenAIProvider(LLMProvider):
    """OpenAI provider implementation."""
    
    def create_llm(self, model: Optional[str] = None, **kwargs) -> LLM:
        """Create OpenAI LLM instance."""
        if not self.validate_config():
            raise ValueError(f"Invalid configuration for OpenAI provider {self.config.name}")
        
        model = model or self.config.default_model
        
        llm_kwargs = {
            "model": model,
            "api_key": self.config.api_key,
            "temperature": kwargs.get("temperature", self.config.temperature),
            "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
            "timeout": kwargs.get("timeout", self.config.timeout),
            "max_retries": kwargs.get("max_retries", self.config.max_retries),
        }

        # Add extra parameters
        llm_kwargs.update(self.config.extra_params)
        llm_kwargs.update(kwargs)
        
        self.logger.info(f"Creating OpenAI LLM with model: {model}")
        return OpenAI(**llm_kwargs)


class OpenAICompatibleProvider(LLMProvider):
    """OpenAI-compatible provider implementation."""
    
    def validate_config(self) -> bool:
        """Validate OpenAI-compatible provider configuration."""
        if not super().validate_config():
            return False
        
        if not self.config.api_base:
            self.logger.error(f"API base URL not configured for provider {self.config.name}")
            return False
        
        return True
    
    def create_llm(self, model: Optional[str] = None, **kwargs) -> LLM:
        """Create OpenAI-compatible LLM instance."""
        if not self.validate_config():
            raise ValueError(f"Invalid configuration for OpenAI-compatible provider {self.config.name}")
        
        model = model or self.config.default_model
        
        llm_kwargs = {
            "model": model,
            "api_base": self.config.api_base,
            "api_key": self.config.api_key,
            "temperature": kwargs.get("temperature", self.config.temperature),
            "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
            "timeout": kwargs.get("timeout", self.config.timeout),
            "max_retries": kwargs.get("max_retries", self.config.max_retries),
            "context_window": kwargs.get("context_window", self.config.context_window),
            "is_chat_model": kwargs.get("is_chat_model", self.config.is_chat_model),
            "is_function_calling_model": kwargs.get("is_function_calling_model", self.config.is_function_calling_model),
            "additional_kwargs": {"stop": ["Observation:"]}
        }
        
        # Add extra parameters
        llm_kwargs.update(self.config.extra_params)
        llm_kwargs.update(kwargs)
        
        self.logger.info(f"Creating OpenAI-compatible LLM with model: {model} at {self.config.api_base}")
        return OpenAILike(**llm_kwargs)

# Provider factory mapping
PROVIDER_CLASSES: Dict[LLMProviderType, type] = {
    LLMProviderType.OPENAI: OpenAIProvider,
    LLMProviderType.OPENAI_COMPATIBLE: OpenAICompatibleProvider,
}

def create_provider(config: LLMProviderConfig) -> LLMProvider:
    """Create a provider instance from configuration."""
    provider_class = PROVIDER_CLASSES.get(config.provider_type)
    
    if not provider_class:
        raise ValueError(f"Unsupported provider type: {config.provider_type}")
    
    return provider_class(config)


def get_supported_provider_types() -> list[LLMProviderType]:
    """Get list of supported provider types."""
    return list(PROVIDER_CLASSES.keys())
