"""
LLM 供应程序管理的 FastAPI 路由。

该模块包含用于管理 LLM 供应程序的所有 API 端点，
包括 CRUD 操作和配置管理。
"""

from fastapi import APIRouter, Depends, HTTPException
from typing import List

from app.auth.security import get_current_internal_user
from app.logger.logger import get_logger, PerformanceLogger
from app.database.services import LLMService
from .models import (
    CreateLLMProviderRequest,
    UpdateLLMProviderRequest,
    LLMProviderSafeResponse,
    LLMProviderDeleteResponse,
    LLMProviderSetDefaultResponse
)

# 初始化路由器和日志记录器
router = APIRouter(prefix="/llms", tags=["LLM 供应程序管理"], dependencies=[Depends(get_current_internal_user)])
api_logger = get_logger("llm.api")


@router.get("", response_model=List[LLMProviderSafeResponse], summary="列出所有 LLM 供应程序")
async def list_llm_providers():
    """列出所有 LLM 供应程序（不含敏感信息）。"""
    try:
        providers = await LLMService.list_llm_providers()
        return [LLMProviderSafeResponse(**provider.to_dict_safe()) for provider in providers]
    except Exception as e:
        api_logger.error("无法列出 LLM 供应程序", exception=e)
        raise HTTPException(status_code=500, detail=f"列出 LLM 供应程序时出错: {str(e)}")


@router.post("", response_model=LLMProviderSafeResponse, summary="创建新的 LLM 供应程序")
async def create_llm_provider(request: CreateLLMProviderRequest):
    """创建一个新的 LLM 供应程序。"""
    try:
        with PerformanceLogger(f"api_create_llm_provider_{request.provider_id}", api_logger):
            provider = await LLMService.create_llm_provider(
                provider_id=request.provider_id,
                name=request.name,
                provider_type=request.provider_type,
                description=request.description,
                api_base=request.api_base,
                api_key=request.api_key,
                default_model=request.default_model,
                context_window=request.context_window,
                max_tokens=request.max_tokens,
                temperature=request.temperature,
                timeout=request.timeout,
                max_retries=request.max_retries,
                is_chat_model=request.is_chat_model,
                is_function_calling_model=request.is_function_calling_model,
                is_default=request.set_as_default,
                extra_params=request.extra_params
            )

            api_logger.info(f"已创建 LLM 供应程序: {request.provider_id}")
            return LLMProviderSafeResponse(**provider.to_dict_safe())
    except ValueError as e:
        api_logger.warning(f"LLM 供应程序创建失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        api_logger.error(f"为 {request.provider_id} 创建 LLM 供应程序失败", exception=e)
        raise HTTPException(status_code=500, detail=f"创建 LLM 供应程序时出错: {str(e)}")


@router.get("/default", response_model=LLMProviderSafeResponse, summary="获取默认的 LLM 供应程序")
async def get_default_llm_provider():
    """获取默认的 LLM 供应程序。"""
    try:
        provider = await LLMService.get_default_llm_provider()
        if not provider:
            raise HTTPException(status_code=404, detail="未找到默认的 LLM 供应程序")
        return LLMProviderSafeResponse(**provider.to_dict_safe())
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error("无法获取默认的 LLM 供应程序", exception=e)
        raise HTTPException(status_code=500, detail=f"获取默认 LLM 供应程序时出错: {str(e)}")


@router.get("/{provider_id}", response_model=LLMProviderSafeResponse, summary="获取特定的 LLM 供应程序")
async def get_llm_provider(provider_id: str):
    """获取一个特定的 LLM 供应程序（不含敏感信息）。"""
    try:
        provider = await LLMService.get_llm_provider_by_id(provider_id)
        if not provider:
            raise HTTPException(status_code=404, detail=f"未找到 LLM 供应程序: {provider_id}")
        return LLMProviderSafeResponse(**provider.to_dict_safe())
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"无法获取 LLM 供应程序: {provider_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"获取 LLM 供应程序时出错: {str(e)}")


@router.put("/{provider_id}", response_model=LLMProviderSafeResponse, summary="更新 LLM 供应程序")
async def update_llm_provider(provider_id: str, request: UpdateLLMProviderRequest):
    """更新一个 LLM 供应程序。"""
    try:
        with PerformanceLogger(f"api_update_llm_provider_{provider_id}", api_logger):
            # 过滤掉 None 值
            updates = {k: v for k, v in request.model_dump().items() if v is not None}

            # 处理 set_as_default 字段
            if 'set_as_default' in updates:
                updates['is_default'] = updates.pop('set_as_default')

            if not updates:
                raise HTTPException(status_code=400, detail="没有有效的字段可更新")

            provider = await LLMService.update_llm_provider(provider_id, **updates)
            if not provider:
                raise HTTPException(status_code=404, detail=f"未找到 LLM 供应程序: {provider_id}")

            api_logger.info(f"已更新 LLM 供应程序: {provider_id}")
            return LLMProviderSafeResponse(**provider.to_dict_safe())
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"无法更新 LLM 供应程序: {provider_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"更新 LLM 供应程序时出错: {str(e)}")


@router.delete("/{provider_id}", response_model=LLMProviderDeleteResponse, summary="删除 LLM 供应程序")
async def delete_llm_provider(provider_id: str):
    """删除一个 LLM 供应程序。"""
    try:
        success = await LLMService.delete_llm_provider(provider_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"未找到 LLM 供应程序: {provider_id}")

        api_logger.info(f"已删除 LLM 供应程序: {provider_id}")
        return LLMProviderDeleteResponse(message=f"LLM 供应程序 '{provider_id}' 已成功删除")
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"无法删除 LLM 供应程序: {provider_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"删除 LLM 供应程序时出错: {str(e)}")


@router.post("/{provider_id}/set-default", response_model=LLMProviderSetDefaultResponse, summary="设置默认的 LLM 供应程序")
async def set_default_llm_provider(provider_id: str):
    """将一个 LLM 供应程序设置为默认。"""
    try:
        success = await LLMService.set_default_llm_provider(provider_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"未找到 LLM 供应程序: {provider_id}")

        api_logger.info(f"已设置默认 LLM 供应程序: {provider_id}")
        return LLMProviderSetDefaultResponse(message=f"LLM 供应程序 '{provider_id}' 已成功设为默认")
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"无法设置默认 LLM 供应程序: {provider_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"设置默认 LLM 供应程序时出错: {str(e)}")


