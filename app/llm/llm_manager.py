"""
LLM Manager for centralized LLM instance management.

This module provides the main LLMManager class that handles LLM instance
creation and lifecycle management with direct database interaction.
"""

import time
import os
import logging
from typing import Dict, Optional, Any, List, Tuple
from llama_index.core.llms import LLM

from .llm_config import LL<PERSON>roviderConfig, LLMProviderType
from .providers import create_provider, LLMProvider
from app.database.services import LLMService

logger = logging.getLogger("llm.manager")

class LLMManager:
    """Main LLM manager class for centralized LLM management with direct database interaction."""

    def __init__(self):
        """Initialize LLM manager."""
        self.logger = logging.getLogger("llm.LLMManager")
        self.logger.info("🚀 LLMManager initialized with direct database interaction")
    
    async def _initialize_default_provider_if_needed(self) -> None:
        """Create default provider from environment variables if not already configured in database."""
        default_api_base = os.getenv("DEFAULT_API_BASE")
        if default_api_base:
            try:
                # Check if default provider already exists in database
                existing_provider = await LLMService.get_llm_provider_by_id("default")
                if not existing_provider:
                    self.logger.info("Found DEFAULT_API_BASE, creating default provider from env vars.")

                    # Create default provider in database
                    await LLMService.create_llm_provider(
                        provider_id="default",
                        name="Default Provider",
                        provider_type="openai_compatible",
                        description="Default provider created from environment variables",
                        api_base=default_api_base,
                        api_key=os.getenv("DEFAULT_API_KEY"),
                        default_model=os.getenv("DEFAULT_MODEL", "gpt-3.5-turbo"),
                        is_default=True
                    )
                    self.logger.info("Created default provider in database")
                else:
                    self.logger.info("Default provider already exists in database")
            except Exception:
                self.logger.error("Failed to create default provider from env vars", exc_info=True)
    
    async def create_llm(
        self,
        provider_id: Optional[str] = None,
        model: Optional[str] = None,
        **kwargs
    ) -> Tuple[LLM, str, str]:
        """
        Create LLM instance by loading provider directly from database.

        Args:
            provider_id: Name of the provider to use (uses default if None)
            model: Model name to use (uses provider default if None)
            **kwargs: Additional parameters for LLM creation

        Returns:
            Tuple[LLM, str, str]: A tuple containing the LLM instance, the actual provider ID used, and the actual model name used.

        Raises:
            ValueError: If provider not found or configuration invalid
        """
        # Initialize default provider if needed
        await self._initialize_default_provider_if_needed()

        # Use default provider if not specified
        actual_provider_id = provider_id
        if not actual_provider_id:
            # Get default provider from database
            default_provider = await LLMService.get_default_llm_provider()
            if default_provider:
                actual_provider_id = str(default_provider.provider_id)
            else:
                raise ValueError("No provider specified and no default provider configured")

        # Load provider directly from database
        self.logger.info(f"Loading provider '{actual_provider_id}' from database.")
        try:
            db_provider = await LLMService.get_llm_provider_by_id(actual_provider_id)
            if not db_provider:
                raise ValueError(f"Provider '{actual_provider_id}' not found in database.")

            # Create provider config from database record
            provider_config = LLMProviderConfig.from_orm(db_provider)
            provider = create_provider(provider_config)

            self.logger.info(f"Successfully loaded provider '{actual_provider_id}' from database.")

        except Exception as e:
            self.logger.error(f"Failed to load provider '{actual_provider_id}' from database: {e}", exc_info=True)
            raise ValueError(f"Provider '{actual_provider_id}' not found or failed to load.")

        # Use provider's default model if not specified
        actual_model = model or provider.config.default_model

        # Create new LLM instance
        try:
            llm = provider.create_llm(model=actual_model, **kwargs)

            self.logger.info(f"Created LLM: provider={actual_provider_id}, model={actual_model}")
            return llm, str(actual_provider_id), actual_model

        except Exception:
            self.logger.error(f"Failed to create LLM: provider={actual_provider_id}, model={actual_model}", exc_info=True)
            raise
    
    def create_llm_from_params(
        self,
        api_base: Optional[str] = None,
        api_key: Optional[str] = None,
        model: str = "gpt-3.5-turbo",
        **kwargs
    ) -> LLM:
        """
        Create LLM instance from direct parameters.
        
        This method is useful for creating one-off LLM instances without
        pre-configured providers.
        
        Args:
            api_base: API base URL (if None, uses OpenAI)
            api_key: API key
            model: Model name
            **kwargs: Additional parameters
        
        Returns:
            LLM: The LLM instance
        """
        # Determine provider type
        if api_base:
            provider_type = LLMProviderType.OPENAI_COMPATIBLE
        else:
            provider_type = LLMProviderType.OPENAI
        
        # Create temporary provider config
        temp_config = LLMProviderConfig(
            provider_type=provider_type,
            name="temp",
            api_base=api_base,
            api_key=api_key,
            default_model=model,
            **kwargs
        )
        
        # Create provider and LLM
        provider = create_provider(temp_config)
        return provider.create_llm(model=model, **kwargs)
    
    async def add_provider(self, provider_config: LLMProviderConfig) -> None:
        """Add a new provider configuration to database."""
        try:
            # Create provider in database
            await LLMService.create_llm_provider(
                provider_id=provider_config.name,
                name=provider_config.name,
                provider_type=provider_config.provider_type.value,
                api_base=provider_config.api_base,
                api_key=provider_config.api_key,
                default_model=provider_config.default_model,
                context_window=provider_config.context_window,
                max_tokens=provider_config.max_tokens,
                temperature=provider_config.temperature,
                timeout=provider_config.timeout,
                max_retries=provider_config.max_retries,
                is_chat_model=provider_config.is_chat_model,
                is_function_calling_model=provider_config.is_function_calling_model,
                extra_params=provider_config.extra_params
            )
            self.logger.info(f"Added provider to database: {provider_config.name}")
        except Exception:
            self.logger.error(f"Failed to add provider {provider_config.name}", exc_info=True)
            raise

    async def remove_provider(self, name: str) -> bool:
        """Remove a provider from database."""
        try:
            result = await LLMService.delete_llm_provider(name)
            if result:
                self.logger.info(f"Removed provider from database: {name}")
                return True
            return False
        except Exception:
            self.logger.error(f"Failed to remove provider {name}", exc_info=True)
            return False
    
    async def list_providers(self) -> List[str]:
        """List all available provider IDs from database."""
        try:
            providers = await LLMService.list_llm_providers()
            return [str(provider.provider_id) for provider in providers]
        except Exception as e:
            self.logger.error(f"Failed to list providers: {e}")
            return []

    async def get_provider_info(self, provider_id: str) -> Optional[LLM]:
        """Get provider information from database."""
        try:
            provider = await LLMService.get_llm_provider_by_id(provider_id)
            if provider:
                # Get provider data and filter out fields that shouldn't be passed to create_llm_from_params
                provider_data = provider.to_dict()

                # Extract the fields needed for create_llm_from_params
                llm_params = {
                    'api_base': provider_data.get('api_base'),
                    'api_key': provider_data.get('api_key'),
                    'model': str(provider.default_model),
                    'temperature': provider_data.get('temperature', 0.7),
                    'max_tokens': provider_data.get('max_tokens'),
                    'timeout': provider_data.get('timeout', 60),
                    'max_retries': provider_data.get('max_retries', 3),
                    'context_window': provider_data.get('context_window', 128000),
                    'is_chat_model': provider_data.get('is_chat_model', True),
                    'is_function_calling_model': provider_data.get('is_function_calling_model', False),
                }

                # Filter out None values
                llm_params = {k: v for k, v in llm_params.items() if v is not None}

                return self.create_llm_from_params(**llm_params)
            return None
        except Exception as e:
            self.logger.error(f"Failed to get provider info for {provider_id}: {e}")
            return None
