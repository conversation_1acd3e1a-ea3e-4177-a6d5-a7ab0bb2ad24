"""
Base Redis cache manager with generic methods.
"""

import dill
import json
from typing import Any, Dict, List, Optional, Union, Type
from enum import Enum
import redis.asyncio as redis
from ..logger.logger import get_logger
from ..config.config import get_config


class SerializationStrategy(Enum):
    """Serialization strategies for cache data."""
    PICKLE = "pickle"
    JSON = "json"
    NONE = "none"  # For string/bytes data


class BaseRedisCache:
    """
    Base Redis cache manager with generic methods.
    
    Provides common caching operations with configurable serialization,
    TTL, and key prefixing for different use cases.
    """
    
    def __init__(
        self, 
        key_prefix: str = "", 
        default_ttl: Optional[int] = None,
        serialization: SerializationStrategy = SerializationStrategy.PICKLE,
        logger_name: Optional[str] = None
    ):
        """
        Initialize Redis cache manager.
        
        Args:
            key_prefix: Prefix for all cache keys
            default_ttl: Default TTL in seconds (uses config if not provided)
            serialization: Serialization strategy for data
            logger_name: Logger name (auto-generated if not provided)
        """
        self.key_prefix = key_prefix
        self.default_ttl = default_ttl
        self.serialization = serialization
        self.logger = get_logger(logger_name or f"cache.{self.__class__.__name__}")
        self._redis_client: Optional[redis.Redis] = None
        
    async def _get_redis_client(self) -> redis.Redis:
        """Get or create Redis client."""
        if self._redis_client is None:
            config = get_config()
            redis_url = getattr(config, 'redis_url', 'redis://localhost:6379/0')
            self._redis_client = redis.from_url(redis_url, decode_responses=False)
            self.logger.debug(f"🔌 Connected to Redis: {redis_url}")
        return self._redis_client
    
    def _make_key(self, key: str) -> str:
        """Create Redis key with prefix."""
        return f"{self.key_prefix}{key}" if self.key_prefix else key
    
    def _get_ttl(self, ttl: Optional[int] = None) -> int:
        """Get TTL value from parameter, instance default, or config."""
        if ttl is not None:
            return ttl
        if self.default_ttl is not None:
            return self.default_ttl
        
        config = get_config()
        return getattr(config, 'redis_cache_ttl', 3600)
    
    def _serialize(self, data: Any) -> bytes:
        """Serialize data based on strategy."""
        if self.serialization == SerializationStrategy.PICKLE:
            return dill.dumps(data)
        elif self.serialization == SerializationStrategy.JSON:
            return json.dumps(data).encode('utf-8')
        elif self.serialization == SerializationStrategy.NONE:
            if isinstance(data, str):
                return data.encode('utf-8')
            elif isinstance(data, bytes):
                return data
            else:
                raise ValueError("NONE serialization requires str or bytes data")
        else:
            raise ValueError(f"Unknown serialization strategy: {self.serialization}")
    
    def _deserialize(self, data: bytes) -> Any:
        """Deserialize data based on strategy."""
        if self.serialization == SerializationStrategy.PICKLE:
            return dill.loads(data)
        elif self.serialization == SerializationStrategy.JSON:
            return json.loads(data.decode('utf-8'))
        elif self.serialization == SerializationStrategy.NONE:
            return data
        else:
            raise ValueError(f"Unknown serialization strategy: {self.serialization}")
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        Set a cache value with optional TTL.
        
        Args:
            key: Cache key (without prefix)
            value: Value to cache
            ttl: TTL in seconds (uses default if not provided)
            
        Returns:
            bool: True if successful
        """
        try:
            redis_client = await self._get_redis_client()
            redis_key = self._make_key(key)
            serialized_value = self._serialize(value)
            expire_time = self._get_ttl(ttl)
            
            await redis_client.setex(redis_key, expire_time, serialized_value)
            
            self.logger.debug(f"📦 Cached key '{key}' (TTL: {expire_time}s)")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cache key '{key}'", exception=e)
            return False
    
    async def get(self, key: str) -> Optional[Any]:
        """
        Get a cache value.
        
        Args:
            key: Cache key (without prefix)
            
        Returns:
            Optional[Any]: Cached value or None if not found/expired
        """
        try:
            redis_client = await self._get_redis_client()
            redis_key = self._make_key(key)
            
            data = await redis_client.get(redis_key)
            if data is None:
                self.logger.debug(f"Cache miss for key '{key}'")
                return None
            
            value = self._deserialize(data)
            self.logger.debug(f"📦 Cache hit for key '{key}'")
            return value
            
        except Exception as e:
            self.logger.error(f"Failed to retrieve key '{key}' from cache", exception=e)
            return None
    
    async def delete(self, key: str) -> bool:
        """
        Delete a cache key.
        
        Args:
            key: Cache key (without prefix)
            
        Returns:
            bool: True if key was deleted, False if not found
        """
        try:
            redis_client = await self._get_redis_client()
            redis_key = self._make_key(key)
            
            result = await redis_client.delete(redis_key)
            
            if result > 0:
                self.logger.debug(f"🗑️ Deleted cache key '{key}'")
                return True
            else:
                self.logger.debug(f"Cache key '{key}' not found for deletion")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to delete key '{key}' from cache", exception=e)
            return False
    
    async def exists(self, key: str) -> bool:
        """
        Check if a cache key exists.
        
        Args:
            key: Cache key (without prefix)
            
        Returns:
            bool: True if key exists
        """
        try:
            redis_client = await self._get_redis_client()
            redis_key = self._make_key(key)
            
            result = await redis_client.exists(redis_key)
            return result > 0
            
        except Exception as e:
            self.logger.error(f"Failed to check existence of key '{key}'", exception=e)
            return False
    
    async def ttl(self, key: str) -> Optional[int]:
        """
        Get remaining TTL for a cache key.
        
        Args:
            key: Cache key (without prefix)
            
        Returns:
            Optional[int]: Remaining TTL in seconds, None if key doesn't exist
        """
        try:
            redis_client = await self._get_redis_client()
            redis_key = self._make_key(key)
            
            ttl_value = await redis_client.ttl(redis_key)
            
            if ttl_value == -2:  # Key doesn't exist
                return None
            elif ttl_value == -1:  # Key exists but has no TTL
                return -1
            else:
                return ttl_value
                
        except Exception as e:
            self.logger.error(f"Failed to get TTL for key '{key}'", exception=e)
            return None
    
    async def mget(self, keys: List[str]) -> Dict[str, Any]:
        """
        Get multiple cache values.
        
        Args:
            keys: List of cache keys (without prefix)
            
        Returns:
            Dict[str, Any]: Dictionary of key-value pairs (only existing keys)
        """
        try:
            redis_client = await self._get_redis_client()
            redis_keys = [self._make_key(key) for key in keys]
            
            values = await redis_client.mget(redis_keys)
            
            result = {}
            for key, value in zip(keys, values):
                if value is not None:
                    try:
                        result[key] = self._deserialize(value)
                    except Exception as e:
                        self.logger.warning(f"Failed to deserialize value for key '{key}': {e}")
            
            self.logger.debug(f"📦 Retrieved {len(result)}/{len(keys)} cache keys")
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to retrieve multiple keys from cache", exception=e)
            return {}
    
    async def mset(self, data: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """
        Set multiple cache values.
        
        Args:
            data: Dictionary of key-value pairs
            ttl: TTL in seconds (uses default if not provided)
            
        Returns:
            bool: True if all keys were set successfully
        """
        try:
            redis_client = await self._get_redis_client()
            expire_time = self._get_ttl(ttl)
            
            # Prepare data for mset
            redis_data = {}
            for key, value in data.items():
                redis_key = self._make_key(key)
                serialized_value = self._serialize(value)
                redis_data[redis_key] = serialized_value
            
            # Set all keys
            await redis_client.mset(redis_data)
            
            # Set TTL for all keys if specified
            if expire_time > 0:
                for redis_key in redis_data.keys():
                    await redis_client.expire(redis_key, expire_time)
            
            self.logger.debug(f"📦 Cached {len(data)} keys (TTL: {expire_time}s)")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cache multiple keys", exception=e)
            return False
    
    async def keys_by_pattern(self, pattern: str = "*") -> List[str]:
        """
        Get keys matching a pattern (without prefix).
        
        Args:
            pattern: Pattern to match (applied after prefix)
            
        Returns:
            List[str]: List of matching keys (without prefix)
        """
        try:
            redis_client = await self._get_redis_client()
            redis_pattern = self._make_key(pattern)
            
            keys = await redis_client.keys(redis_pattern)
            
            # Remove prefix from keys
            result = []
            for key in keys:
                key_str = key.decode('utf-8') if isinstance(key, bytes) else key
                if self.key_prefix and key_str.startswith(self.key_prefix):
                    result.append(key_str[len(self.key_prefix):])
                else:
                    result.append(key_str)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to get keys by pattern '{pattern}'", exception=e)
            return []
    
    async def clear_by_pattern(self, pattern: str = "*") -> int:
        """
        Clear all keys matching a pattern.
        
        Args:
            pattern: Pattern to match (applied after prefix)
            
        Returns:
            int: Number of keys deleted
        """
        try:
            redis_client = await self._get_redis_client()
            redis_pattern = self._make_key(pattern)
            
            keys = await redis_client.keys(redis_pattern)
            
            if keys:
                count = await redis_client.delete(*keys)
                self.logger.info(f"🗑️ Cleared {count} cache entries matching '{pattern}'")
                return count
            else:
                self.logger.debug(f"No cache entries found matching '{pattern}'")
                return 0
                
        except Exception as e:
            self.logger.error(f"Failed to clear cache entries matching '{pattern}'", exception=e)
            return 0
    
    async def close(self) -> None:
        """Close Redis connection."""
        if self._redis_client:
            await self._redis_client.close()
            self._redis_client = None
            self.logger.debug("🔌 Redis connection closed")
