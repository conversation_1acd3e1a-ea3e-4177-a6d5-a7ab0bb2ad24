"""
Redis cache manager for MCP tools using generic base cache.
"""

from typing import List, Optional
from llama_index.core.tools import BaseTool
from .base_cache import BaseRedisCache, SerializationStrategy


class RedisToolCache(BaseRedisCache):
    """
    Redis-based cache manager for MCP tools to support distributed deployment.
    
    Inherits from BaseRedisCache and provides MCP tool-specific caching methods.
    """
    
    def __init__(self):
        """Initialize Redis tool cache with MCP-specific configuration."""
        super().__init__(
            key_prefix="mcp_tools:",
            serialization=SerializationStrategy.PICKLE,
            logger_name="cache.RedisToolCache"
        )
    
    async def set_server_tools(self, server_id: str, tools: List[BaseTool], ttl: Optional[int] = None) -> None:
        """
        Store tools for a specific MCP server in Redis with TTL.
        
        Args:
            server_id: The MCP server ID
            tools: List of tools to store
            ttl: Time to live in seconds (uses config default if not provided)
            
        Raises:
            Exception: If caching fails
        """
        success = await self.set(server_id, tools, ttl)
        if not success:
            raise Exception(f"Failed to cache tools for server '{server_id}'")
        
        ttl_used = self._get_ttl(ttl)
        self.logger.info(f"📦 Cached {len(tools)} tools for server '{server_id}' (TTL: {ttl_used}s)")
    
    async def get_server_tools_from_cache(self, server_id: str) -> Optional[List[BaseTool]]:
        """
        Retrieve tools for a specific MCP server from Redis cache only.
        
        Args:
            server_id: The MCP server ID
            
        Returns:
            Optional[List[BaseTool]]: List of tools from cache, None if not found or expired
        """
        tools = await self.get(server_id)
        
        if tools is None:
            self.logger.debug(f"No cached tools found for server '{server_id}'")
            return None
        
        if not isinstance(tools, list):
            self.logger.warning(f"Invalid cached data type for server '{server_id}': expected list, got {type(tools)}")
            return None
        
        self.logger.debug(f"📦 Retrieved {len(tools)} tools for server '{server_id}' from cache")
        return tools
    
    async def remove_server_tools(self, server_id: str) -> bool:
        """
        Remove tools for a specific MCP server from Redis.
        
        Args:
            server_id: The MCP server ID
            
        Returns:
            bool: True if tools were removed, False if not found
        """
        result = await self.delete(server_id)
        
        if result:
            self.logger.info(f"🗑️ Removed cached tools for server '{server_id}'")
        else:
            self.logger.debug(f"No cached tools found to remove for server '{server_id}'")
        
        return result
    
    async def get_all_tools(self) -> List[BaseTool]:
        """
        Get all tools from all MCP servers in cache.
        
        Returns:
            List[BaseTool]: List of all cached tools
        """
        try:
            # Get all server IDs that have cached tools
            server_ids = await self.keys_by_pattern("*")
            
            all_tools = []
            for server_id in server_ids:
                tools = await self.get_server_tools_from_cache(server_id)
                if tools:
                    all_tools.extend(tools)
            
            self.logger.debug(f"📦 Retrieved {len(all_tools)} total tools from {len(server_ids)} cached servers")
            return all_tools
            
        except Exception as e:
            self.logger.error("Failed to retrieve all tools from cache", exception=e)
            return []
    
    async def list_cached_servers(self) -> List[str]:
        """
        List all server IDs that have cached tools.
        
        Returns:
            List[str]: List of server IDs with cached tools
        """
        try:
            server_ids = await self.keys_by_pattern("*")
            return server_ids
            
        except Exception as e:
            self.logger.error("Failed to list cached servers", exception=e)
            return []
    
    async def clear_all(self) -> int:
        """
        Clear all cached tools.
        
        Returns:
            int: Number of keys removed
        """
        try:
            count = await self.clear_by_pattern("*")
            
            if count > 0:
                self.logger.info(f"🗑️ Cleared {count} cached tool entries")
            else:
                self.logger.info("No cached tools to clear")
            
            return count
            
        except Exception as e:
            self.logger.error("Failed to clear cached tools", exception=e)
            return 0
    
    async def get_cache_ttl(self, server_id: str) -> Optional[int]:
        """
        Get remaining TTL for cached tools.

        Args:
            server_id: The MCP server ID

        Returns:
            Optional[int]: Remaining TTL in seconds, None if key doesn't exist
        """
        return await self.ttl(server_id)
    
    async def refresh_cache_ttl(self, server_id: str, ttl: Optional[int] = None) -> bool:
        """
        Refresh the TTL for cached tools without updating the content.
        
        Args:
            server_id: The MCP server ID
            ttl: New TTL in seconds (uses config default if not provided)
            
        Returns:
            bool: True if TTL was refreshed, False if key doesn't exist
        """
        try:
            # Get current tools
            tools = await self.get_server_tools_from_cache(server_id)
            if tools is None:
                self.logger.debug(f"Key not found for server '{server_id}' when refreshing TTL")
                return False
            
            # Re-set with new TTL
            ttl_used = self._get_ttl(ttl)
            success = await self.set(server_id, tools, ttl_used)
            
            if success:
                self.logger.debug(f"🔄 Refreshed TTL for server '{server_id}' tools (TTL: {ttl_used}s)")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to refresh TTL for server '{server_id}'", exception=e)
            return False


# Global Redis tool cache instance
redis_tool_cache = RedisToolCache()