"""
Logging configuration for the agents module.
"""

import logging
import sys
import traceback
from pathlib import Path
from typing import Optional
from datetime import datetime


class AgentLogger:
    """
    Custom logger for the agents module with enhanced error reporting.
    """
    
    def __init__(self, name: str = "agents", level: int = logging.INFO):
        """
        Initialize the logger.

        Args:
            name: Logger name
            level: Logging level
        """
        self.logger = logging.getLogger(name)
        self.logger.setLevel(level)

        # Prevent propagation to root logger to avoid duplicate messages
        self.logger.propagate = False

        # Avoid duplicate handlers
        if not self.logger.handlers:
            self._setup_handlers()
    
    def _setup_handlers(self):
        """Set up console and file handlers."""
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # File handler
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        file_handler = logging.FileHandler(
            log_dir / f"agents_{datetime.now().strftime('%Y%m%d')}.log"
        )
        file_handler.setLevel(logging.DEBUG)
        
        # Formatters
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        console_handler.setFormatter(console_formatter)
        file_handler.setFormatter(file_formatter)
        
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
    
    def debug(self, message: str, **kwargs):
        """Log debug message."""
        self.logger.debug(message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """Log info message."""
        self.logger.info(message, **kwargs)
    
    def warning(self, message: str, exception: Optional[Exception] = None, **kwargs):
        """Log warning message with optional exception details."""
        if exception:
            self.logger.warning(f"{message}: {str(exception)}", **kwargs)
            self.logger.warning("Full traceback:", exc_info=True)
            print(f"⚠️ WARNING: {message}: {str(exception)}", file=sys.stderr)
        else:
            self.logger.warning(message, **kwargs)
    
    def error(self, message: str, exception: Optional[Exception] = None, **kwargs):
        """
        Log error message with optional exception details.
        
        Args:
            message: Error message
            exception: Exception object to log
            **kwargs: Additional logging arguments
        """
        if exception:
            # Log the error message
            self.logger.error(f"{message}: {str(exception)}", **kwargs)
            
            # Log the full traceback
            self.logger.error("Full traceback:", exc_info=True)
            
            # Also print to console for immediate visibility
            print(f"❌ ERROR: {message}: {str(exception)}", file=sys.stderr)
            print("📋 Full traceback:", file=sys.stderr)
            traceback.print_exc()
        else:
            self.logger.error(message, **kwargs)
            print(f"❌ ERROR: {message}", file=sys.stderr)
    
    def critical(self, message: str, exception: Optional[Exception] = None, **kwargs):
        """
        Log critical message with optional exception details.
        
        Args:
            message: Critical message
            exception: Exception object to log
            **kwargs: Additional logging arguments
        """
        if exception:
            self.logger.critical(f"{message}: {str(exception)}", **kwargs)
            self.logger.critical("Full traceback:", exc_info=True)
            print(f"🚨 CRITICAL: {message}: {str(exception)}", file=sys.stderr)
            print("📋 Full traceback:", file=sys.stderr)
            traceback.print_exc()
        else:
            self.logger.critical(message, **kwargs)
            print(f"🚨 CRITICAL: {message}", file=sys.stderr)
    
    def log_agent_operation(self, operation: str, agent_id: str, details: Optional[str] = None):
        """
        Log agent-specific operations.
        
        Args:
            operation: Operation being performed
            agent_id: Agent identifier
            details: Additional details
        """
        message = f"🤖 Agent '{agent_id}': {operation}"
        if details:
            message += f" - {details}"
        self.info(message)
    
    def log_tool_operation(self, operation: str, tool_name: str, agent_id: Optional[str] = None):
        """
        Log tool-specific operations.
        
        Args:
            operation: Operation being performed
            tool_name: Tool name
            agent_id: Agent identifier (if applicable)
        """
        message = f"🛠️ Tool '{tool_name}': {operation}"
        if agent_id:
            message += f" (Agent: {agent_id})"
        self.info(message)
    
    def log_chat_interaction(self, agent_id: str, message_preview: str, response_preview: str):
        """
        Log chat interactions.
        
        Args:
            agent_id: Agent identifier
            message_preview: Preview of user message
            response_preview: Preview of agent response
        """
        # Truncate long messages for logging
        msg_preview = message_preview[:100] + "..." if len(message_preview) > 100 else message_preview
        resp_preview = response_preview[:100] + "..." if len(response_preview) > 100 else response_preview
        
        self.info(f"💬 Chat with '{agent_id}' - User: '{msg_preview}' -> Agent: '{resp_preview}'")
    
    def log_performance(self, operation: str, duration: float, details: Optional[str] = None):
        """
        Log performance metrics.
        
        Args:
            operation: Operation name
            duration: Duration in seconds
            details: Additional details
        """
        message = f"⏱️ Performance: {operation} took {duration:.2f}s"
        if details:
            message += f" - {details}"
        self.debug(message)


# Global logger instance
logger = AgentLogger()


def get_logger(name: str = "agents") -> AgentLogger:
    """
    Get a logger instance.
    
    Args:
        name: Logger name
        
    Returns:
        AgentLogger: Logger instance
    """
    return AgentLogger(name)


def log_exception(message: str, exception: Exception, logger_instance: Optional[AgentLogger] = None):
    """
    Convenience function to log exceptions with full traceback.
    
    Args:
        message: Error message
        exception: Exception to log
        logger_instance: Logger instance to use (uses global if None)
    """
    log_instance = logger_instance or logger
    log_instance.error(message, exception=exception)


def setup_logging(level: int = logging.INFO, log_to_file: bool = True):
    """
    Set up logging configuration for the entire agents module.
    
    Args:
        level: Logging level
        log_to_file: Whether to log to file
    """
    # Configure root logger
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Set up file logging if requested
    if log_to_file:
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        file_handler = logging.FileHandler(
            log_dir / f"agents_full_{datetime.now().strftime('%Y%m%d')}.log"
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(
            logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
        )
        
        # Add to root logger
        logging.getLogger().addHandler(file_handler)
    
    logger.info("🚀 Logging system initialized")


# Context manager for performance logging
class PerformanceLogger:
    """Context manager for logging operation performance."""
    
    def __init__(self, operation: str, logger_instance: Optional[AgentLogger] = None):
        self.operation = operation
        self.logger = logger_instance or logger
        self.start_time = None
    
    def __enter__(self):
        import time
        self.start_time = time.time()
        self.logger.debug(f"⏱️ Starting: {self.operation}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        import time
        duration = time.time() - self.start_time
        
        if exc_type is not None:
            self.logger.error(f"❌ Failed: {self.operation} after {duration:.2f}s", exception=exc_val)
        else:
            self.logger.log_performance(self.operation, duration)


# Decorator for automatic error logging
def log_errors(operation_name: str = None):
    """
    Decorator to automatically log errors with full traceback.
    
    Args:
        operation_name: Name of the operation (uses function name if None)
    """
    def decorator(func):
        import functools
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            op_name = operation_name or func.__name__
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"Error in {op_name}", exception=e)
                raise
        
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            op_name = operation_name or func.__name__
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                logger.error(f"Error in {op_name}", exception=e)
                raise
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else wrapper
    return decorator
