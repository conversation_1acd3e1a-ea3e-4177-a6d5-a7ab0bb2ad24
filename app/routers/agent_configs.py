from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import Optional, List, Dict, Any
from app.agent_configs.schemas import AgentConfigUpsertRequest, AgentConfigResponse

from app.routers.dependencies import get_db_session
from app.auth.security import get_current_internal_user
from app.database.models import AgentConfig

router = APIRouter(prefix="/agent-configs", tags=["Agent 配置管理"],
                   responses={404: {"description": "未找到"}})


@router.post(
    "/upsert-by-agent-id",
    summary="按 agent_id 创建或更新 Agent 配置",
    response_model=AgentConfigResponse,
    dependencies=[Depends(get_current_internal_user)],
)
async def upsert_agent_config_by_agent_id(
    payload: AgentConfigUpsertRequest,
    session: AsyncSession = Depends(get_db_session),
):
    """根据传入的 agent_id 对 Agent 配置进行创建或更新。需要内部用户鉴权。\n\n可选字段：icon、is_public（public/private/partial）、user_scope（数组）、is_pinned、pin_order、chat_recommend（数组）、extra 对象。"""
    agent_id = payload.agent_id

    result = await session.execute(select(AgentConfig).where(AgentConfig.agent_id == agent_id))
    config: Optional[AgentConfig] = result.scalars().first()

    if config is None:
        config = AgentConfig(agent_id=agent_id)
        session.add(config)

    if payload.icon is not None:
        config.icon = payload.icon
    if payload.is_public is not None:
        config.is_public = payload.is_public
    if payload.user_scope is not None:
        config.user_scope = payload.user_scope
    if payload.is_pinned is not None:
        config.is_pinned = payload.is_pinned
    if payload.pin_order is not None:
        config.pin_order = payload.pin_order
    if payload.chat_recommend is not None:
        config.chat_recommend = payload.chat_recommend
    if payload.extra is not None:
        config.extra = payload.extra

    await session.flush()
    await session.refresh(config)
    data = config.to_dict()
    return AgentConfigResponse(**data)