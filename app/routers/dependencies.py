"""
FastAPI dependencies for the application.
"""

from typing import As<PERSON><PERSON>enerator
from sqlalchemy.ext.asyncio import AsyncSession
from app.database.connection import get_database_manager


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    FastAPI dependency to provide a database session.
    
    This ensures that each request gets a session from the connection pool,
    and that the session is properly closed and the transaction is
    committed or rolled back.
    """
    db_manager = get_database_manager()
    async with db_manager.get_session() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
