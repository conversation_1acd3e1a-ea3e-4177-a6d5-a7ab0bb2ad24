"""
用户端交互的数据结构定义
"""

from pydantic import BaseModel, Field
from typing import List


class PublicAgentInfo(BaseModel):
    """公开的智能体信息"""
    agent_id: str = Field(..., description="智能体ID")
    name: str = Field(..., description="智能体名称")
    description: str = Field(..., description="智能体描述")
    is_default: bool = Field(..., description="是否为默认智能体")


class PublicAgentsResponse(BaseModel):
    """公开智能体列表响应"""
    public_agents: List[PublicAgentInfo] = Field(..., description="公开的智能体列表")
    partial_access_agents: List[PublicAgentInfo] = Field(..., description="partial 且当前用户有访问权限的智能体列表")
    total: int = Field(..., description="智能体总数")