"""
用户端交互的 FastAPI 路由

提供面向用户的API接口
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional

from app.agents.models import AgentSafeResponse
from app.logger.logger import get_logger
from app.database.services import AgentService
from app.auth.security import get_optional_current_user, get_current_user
from app.auth.schemas import User
from .schemas import PublicAgentInfo, PublicAgentsResponse

router = APIRouter(prefix="/front/agents", tags=["用户端接口"])
api_logger = get_logger("front.api")


@router.get("", response_model=PublicAgentsResponse, summary="获取可用的智能体列表")
async def get_public_agents(current_user: Optional[User] = Depends(get_optional_current_user)):
    """
    获取智能体列表，包含两部分：
    1. public_agents: 所有用户都可见的公开智能体
    2. partial_access_agents: 仅当前用户有访问权限的部分可见智能体（需要认证）
    
    无需认证即可访问，但认证用户可以获取更多智能体
    """
    try:
        # 获取公开智能体
        public_agents_data = await AgentService.list_public_agents()
        
        public_agents = []
        for agent in public_agents_data:
            public_agent = PublicAgentInfo(
                agent_id=agent.agent_id,
                name=agent.name,
                description=agent.description or "",
                is_default=agent.is_default
            )
            public_agents.append(public_agent)
        
        # 获取partial权限智能体（需要用户认证）
        partial_access_agents = []
        if current_user:
            partial_agents_data = await AgentService.list_partial_access_agents(current_user.id)
            for agent in partial_agents_data:
                partial_agent = PublicAgentInfo(
                    agent_id=agent.agent_id,
                    name=agent.name,
                    description=agent.description or "",
                    is_default=agent.is_default
                )
                partial_access_agents.append(partial_agent)
        
        api_logger.info(f"返回公开智能体列表，共 {len(public_agents)} 个公开智能体，{len(partial_access_agents)} 个部分可见智能体")
        
        return PublicAgentsResponse(
            public_agents=public_agents,
            partial_access_agents=partial_access_agents,
            total=len(public_agents) + len(partial_access_agents)
        )
        
    except Exception as e:
        api_logger.error("获取公开智能体列表失败", exception=e)
        raise HTTPException(
            status_code=500, 
            detail=f"获取智能体列表时出错: {str(e)}"
        )

@router.get("/{agent_id}", response_model=AgentSafeResponse, summary="获取特定智能体的信息")
async def get_agent(agent_id: str, current_user: Optional[User] = Depends(get_current_user)):
    """获取特定智能体的信息。"""
    try:
        agent = await AgentService.get_agent_by_id(agent_id)
        if not agent:
            raise HTTPException(status_code=404, detail=f"在数据库中未找到智能体: {agent_id}")
        from sqlalchemy import select
        from app.database.connection import get_database_manager
        from app.database.models import AgentConfig
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            res = await session.execute(select(AgentConfig).where(AgentConfig.agent_id == agent.agent_id))
            cfg = res.scalars().first()
        agent_dict = agent.to_dict()
        agent_dict["agent_config"] = cfg.to_dict() if cfg else {}
        return AgentSafeResponse(**agent_dict)
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"从数据库获取智能体失败: {agent_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"从数据库获取智能体时出错: {str(e)}")