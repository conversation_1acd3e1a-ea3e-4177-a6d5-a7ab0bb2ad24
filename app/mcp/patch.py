from typing import Any, Dict, Optional, Type

from llama_index.core.tools.types import ToolMetadata
from pydantic import BaseModel, Field, create_model
import llama_index.tools.mcp.base as mcp_base

# Global cache for dynamically created Pydantic models
_dynamic_models: Dict[str, Type[BaseModel]] = {}

# Map JSON Schema types to Python types
json_type_mapping: Dict[str, Type] = {
    "string": str,
    "number": float,
    "integer": int,
    "boolean": bool,
    "object": dict,
    "array": list,
}


def patched_create_model_from_json_schema(
    schema: Dict[str, Any], model_name: str = "DynamicModel"
) -> Type[BaseModel]:
    """
    To create a Pydantic model from the JSON Schema of MCP tools.
    This patched version stores the created model in a global dictionary
    to make it serializable by dill.
    """
    properties = schema.get("properties", {})
    required_fields = set(schema.get("required", []))
    fields = {}

    for field_name, field_schema in properties.items():
        json_type = field_schema.get("type", "string")
        json_type = json_type[0] if isinstance(json_type, list) else json_type

        field_type = json_type_mapping.get(json_type, str)
        if field_name in required_fields:
            default_value = ...
        else:
            default_value = None
            field_type = Optional[field_type]
        fields[field_name] = (
            field_type,
            Field(default_value, description=field_schema.get("description", "")),
        )

    # Create the model
    model = create_model(model_name, **fields)

    # Store the dynamically created model in the global cache
    _dynamic_models[model_name] = model

    return model


def apply_patch():
    """Applies the monkey patch."""
    mcp_base.create_model_from_json_schema = patched_create_model_from_json_schema
