"""
MCP (模型上下文协议) 数据模型。
"""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from enum import Enum


class MCPTransportType(str, Enum):
    """MCP 传输类型。"""
    STDIO = "stdio"
    SSE = "sse"
    HTTP = "http"


class MCPServerConfig(BaseModel):
    """MCP 服务器的配置。"""
    name: str = Field(..., description="服务器的人性化名称")
    server_id: str = Field(..., description="服务器的唯一标识符")
    description: Optional[str] = Field(None, description="服务器的描述")
    command: Optional[str] = Field(None, description="启动 MCP 服务器的命令")
    args: Optional[List[str]] = Field(default_factory=list, description="服务器命令的参数")
    env: Optional[Dict[str, str]] = Field(default_factory=dict, description="环境变量")
    type: MCPTransportType = Field(default=MCPTransportType.STDIO, description="MCP 通信的传输类型")
    server_url: Optional[str] = Field(default=None, description="SSE 端点 URL (SSE 传输类型必需)")
    is_active: bool = Field(default=True, description="服务器是否激活")
    timeout: int = Field(default=30, description="连接超时时间 (秒)")
    max_retries: int = Field(default=3, description="最大连接重试次数")
    extra_config: Optional[Dict[str, Any]] = Field(None, description="附加配置")


class CreateMCPServerRequest(BaseModel):
    """创建新 MCP 服务器的请求模型。"""
    name: str = Field(..., description="服务器的人性化名称")
    description: Optional[str] = Field(None, description="服务器的描述")
    command: Optional[str] = Field(None, description="启动 MCP 服务器的命令")
    args: Optional[List[str]] = Field(None, description="服务器命令的参数")
    env: Optional[Dict[str, str]] = Field(None, description="环境变量")
    type: MCPTransportType = Field(default=MCPTransportType.STDIO, description="MCP 通信的传输类型")
    serverUrl: Optional[str] = Field(None, description="SSE 端点 URL (SSE 传输类型必需)")
    isActive: bool = Field(default=True, description="服务器是否激活")
    timeout: int = Field(default=60, description="连接超时时间 (秒)")
    maxRetries: int = Field(default=3, description="最大连接重试次数")
    extraConfig: Optional[Dict[str, Any]] = Field(None, description="附加配置")


class UpdateMCPServerRequest(BaseModel):
    """更新 MCP 服务器的请求模型。"""
    serverId: Optional[str] = Field(None, description="服务器的唯一标识符")
    name: Optional[str] = Field(None, description="服务器的人性化名称")
    description: Optional[str] = Field(None, description="服务器的描述")
    command: Optional[str] = Field(None, description="启动 MCP 服务器的命令")
    args: Optional[List[str]] = Field(None, description="服务器命令的参数")
    env: Optional[Dict[str, str]] = Field(None, description="环境变量")
    type: Optional[MCPTransportType] = Field(None, description="MCP 通信的传输类型")
    serverUrl: Optional[str] = Field(None, description="SSE 端点 URL (SSE 传输类型必需)")
    isActive: Optional[bool] = Field(None, description="服务器是否激活")
    timeout: Optional[int] = Field(None, description="连接超时时间 (秒)")
    maxRetries: Optional[int] = Field(None, description="最大连接重试次数")
    extraConfig: Optional[Dict[str, Any]] = Field(None, description="附加配置")


class MCPServerResponse(BaseModel):
    """MCP 服务器的完整响应模型 (包含所有信息)。"""
    id: int
    server_id: str
    name: str
    description: Optional[str]
    command: str
    args: List[str]
    env: Dict[str, str]
    type: str
    server_url: Optional[str]
    is_active: bool
    timeout: int
    max_retries: int
    extra_config: Optional[Dict[str, Any]]
    created_at: str
    updated_at: str


class MCPServerSafeResponse(BaseModel):
    """MCP 服务器的安全响应模型 (不含敏感信息)。"""
    id: int
    server_id: str
    name: str
    description: Optional[str]
    command: Optional[str]
    args: List[str]
    env: Optional[Dict[str, str]]
    type: str
    server_url: Optional[str]
    is_active: bool
    timeout: int
    max_retries: int
    created_at: str
    updated_at: str


class MCPServerDeleteResponse(BaseModel):
    """MCP 服务器删除的响应模型。"""
    message: str
    server_id: str


class MCPToolResponse(BaseModel):
    """MCP 工具的响应模型。"""
    server_id: str
    server_name: str
    tools: List[Dict[str, Any]]


class MCPServerStatus(BaseModel):
    """MCP 服务器的状态信息。"""
    server_id: str
    name: str
    is_running: bool
    is_connected: bool
    last_error: Optional[str] = None
    uptime: Optional[int] = None  # seconds
    tool_count: int = 0
    resource_count: int = 0
