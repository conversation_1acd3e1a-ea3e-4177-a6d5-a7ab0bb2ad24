"""
知识库管理的API端点。
"""

import asyncio
import os
from typing import List, Optional
from pathlib import Path

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from fastapi.responses import J<PERSON>NResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.auth.security import get_current_user
from app.auth.models import User
from ..routers.dependencies import get_db_session
from app.knowledge_base.services import knowledge_base_service, document_service
from app.knowledge_base.vectorization import vectorization_service
from app.knowledge_base.schemas import (
    KnowledgeBaseCreate,
    KnowledgeBaseUpdate,
    KnowledgeBaseResponse,
    DocumentResponse,
    DocumentSearch,
    SearchResult,
    KnowledgeBaseStats
)
from app.logger.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/knowledge", tags=["knowledge"])


@router.post("/bases", response_model=KnowledgeBaseResponse)
async def create_knowledge_base(
    kb_data: KnowledgeBaseCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """创建新的知识库。"""
    try:
        knowledge_base = await knowledge_base_service.create_knowledge_base(
            name=kb_data.name,
            description=kb_data.description,
            created_by=current_user.username,
            is_public=kb_data.is_public,
            config=kb_data.config
        )
        return KnowledgeBaseResponse(**knowledge_base.to_dict())
    except Exception as e:
        logger.error(f"Failed to create knowledge base: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/bases", response_model=List[KnowledgeBaseResponse])
async def list_knowledge_bases(
    include_public: bool = True,
    limit: int = 100,
    offset: int = 0,
    current_user: User = Depends(get_current_user)
):
    """列出知识库。"""
    try:
        knowledge_bases = await knowledge_base_service.list_knowledge_bases(
            created_by=current_user.username,
            include_public=include_public,
            limit=limit,
            offset=offset
        )
        return [KnowledgeBaseResponse(**kb.to_dict()) for kb in knowledge_bases]
    except Exception as e:
        logger.error(f"Failed to list knowledge bases: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/bases/{kb_id}", response_model=KnowledgeBaseResponse)
async def get_knowledge_base(
    kb_id: str,
    current_user: User = Depends(get_current_user)
):
    """根据ID获取知识库。"""
    knowledge_base = await knowledge_base_service.get_knowledge_base(kb_id)
    if not knowledge_base:
        raise HTTPException(status_code=404, detail="Knowledge base not found")
    
    # Check access
    if not knowledge_base.is_public and knowledge_base.created_by != current_user.username:
        raise HTTPException(status_code=403, detail="Access denied")
    
    return KnowledgeBaseResponse(**knowledge_base.to_dict())


@router.put("/bases/{kb_id}", response_model=KnowledgeBaseResponse)
async def update_knowledge_base(
    kb_id: str,
    kb_data: KnowledgeBaseUpdate,
    current_user: User = Depends(get_current_user)
):
    """更新知识库。"""
    knowledge_base = await knowledge_base_service.get_knowledge_base(kb_id)
    if not knowledge_base:
        raise HTTPException(status_code=404, detail="Knowledge base not found")
    
    # Check ownership
    if knowledge_base.created_by != current_user.username:
        raise HTTPException(status_code=403, detail="Access denied")
    
    try:
        updated_kb = await knowledge_base_service.update_knowledge_base(
            kb_id=kb_id,
            name=kb_data.name,
            description=kb_data.description,
            is_public=kb_data.is_public,
            config=kb_data.config
        )
        if not updated_kb:
            raise HTTPException(status_code=404, detail="Knowledge base not found")
        return KnowledgeBaseResponse(**updated_kb.to_dict())
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update knowledge base: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/bases/{kb_id}")
async def delete_knowledge_base(
    kb_id: str,
    current_user: User = Depends(get_current_user)
):
    """删除知识库。"""
    knowledge_base = await knowledge_base_service.get_knowledge_base(kb_id)
    if not knowledge_base:
        raise HTTPException(status_code=404, detail="Knowledge base not found")
    
    # Check ownership
    if knowledge_base.created_by != current_user.username:
        raise HTTPException(status_code=403, detail="Access denied")
    
    try:
        success = await knowledge_base_service.delete_knowledge_base(kb_id)
        if success:
            # Delete ChromaDB collection
            await vectorization_service.delete_collection(kb_id)
            return {"message": "Knowledge base deleted successfully"}
        else:
            raise HTTPException(status_code=404, detail="Knowledge base not found")
    except Exception as e:
        logger.error(f"Failed to delete knowledge base: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/bases/{kb_id}/documents", response_model=DocumentResponse)
async def upload_document(
    kb_id: str,
    file: UploadFile = File(...),
    metadata: Optional[str] = Form(None),
    current_user: User = Depends(get_current_user)
):
    """上传文档到知识库。"""
    knowledge_base = await knowledge_base_service.get_knowledge_base(kb_id)
    if not knowledge_base:
        raise HTTPException(status_code=404, detail="Knowledge base not found")
    
    # Check access
    if not knowledge_base.is_public and knowledge_base.created_by != current_user.username:
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Validate file
    if not file.filename:
        raise HTTPException(status_code=400, detail="Invalid file")
    
    # Check file size (limit to 10MB)
    content = await file.read()
    if len(content) > 10 * 1024 * 1024:
        raise HTTPException(status_code=400, detail="File too large (max 10MB)")
    
    # Validate file type
    allowed_types = [
        'text/plain',
        'text/markdown',
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ]
    
    if file.content_type not in allowed_types:
        raise HTTPException(status_code=400, detail="Unsupported file type")
    
    try:
        import json
        metadata_dict = json.loads(metadata) if metadata else {}
        
        document = await document_service.upload_document(
            kb_id=kb_id,
            filename=file.filename,
            file_content=content,
            content_type=file.content_type,
            metadata=metadata_dict
        )
        
        # Process document asynchronously
        asyncio.create_task(vectorization_service.process_document(document.doc_id))
        
        return DocumentResponse(**document.to_dict())
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to upload document: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/bases/{kb_id}/documents", response_model=List[DocumentResponse])
async def list_documents(
    kb_id: str,
    status: Optional[str] = None,
    limit: int = 100,
    offset: int = 0,
    current_user: User = Depends(get_current_user)
):
    """列出知识库中的文档。"""
    knowledge_base = await knowledge_base_service.get_knowledge_base(kb_id)
    if not knowledge_base:
        raise HTTPException(status_code=404, detail="Knowledge base not found")
    
    # Check access
    if not knowledge_base.is_public and knowledge_base.created_by != current_user.username:
        raise HTTPException(status_code=403, detail="Access denied")
    
    try:
        documents = await document_service.list_documents(
            kb_id=kb_id,
            status=status,
            limit=limit,
            offset=offset
        )
        return [DocumentResponse(**doc.to_dict()) for doc in documents]
    except Exception as e:
        logger.error(f"Failed to list documents: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/documents/{doc_id}", response_model=DocumentResponse)
async def get_document(
    doc_id: str,
    current_user: User = Depends(get_current_user)
):
    """根据ID获取文档。"""
    document = await document_service.get_document(doc_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Check access
    knowledge_base = await knowledge_base_service.get_knowledge_base(document.kb_id)
    if not knowledge_base:
        raise HTTPException(status_code=404, detail="Knowledge base not found")
    
    if not knowledge_base.is_public and knowledge_base.created_by != current_user.username:
        raise HTTPException(status_code=403, detail="Access denied")
    
    return DocumentResponse(**document.to_dict())


@router.delete("/documents/{doc_id}")
async def delete_document(
    doc_id: str,
    current_user: User = Depends(get_current_user)
):
    """删除文档。"""
    document = await document_service.get_document(doc_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Check access
    knowledge_base = await knowledge_base_service.get_knowledge_base(document.kb_id)
    if not knowledge_base:
        raise HTTPException(status_code=404, detail="Knowledge base not found")
    
    if not knowledge_base.is_public and knowledge_base.created_by != current_user.username:
        raise HTTPException(status_code=403, detail="Access denied")
    
    try:
        success = await document_service.delete_document(doc_id)
        if success:
            return {"message": "Document deleted successfully"}
        else:
            raise HTTPException(status_code=404, detail="Document not found")
    except Exception as e:
        logger.error(f"Failed to delete document: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/bases/{kb_id}/search", response_model=List[SearchResult])
async def search_documents(
    kb_id: str,
    search_data: DocumentSearch,
    current_user: User = Depends(get_current_user)
):
    """在知识库中搜索文档。"""
    knowledge_base = await knowledge_base_service.get_knowledge_base(kb_id)
    if not knowledge_base:
        raise HTTPException(status_code=404, detail="Knowledge base not found")
    
    # Check access
    if not knowledge_base.is_public and knowledge_base.created_by != current_user.username:
        raise HTTPException(status_code=403, detail="Access denied")
    
    try:
        results = await vectorization_service.search_similar_chunks(
            kb_id=kb_id,
            query=search_data.query,
            limit=search_data.limit,
            doc_ids=search_data.doc_ids
        )
        return [SearchResult(**result) for result in results]
    except Exception as e:
        logger.error(f"Failed to search documents: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/bases/{kb_id}/stats", response_model=KnowledgeBaseStats)
async def get_knowledge_base_stats(
    kb_id: str,
    current_user: User = Depends(get_current_user)
):
    """获取知识库统计信息。"""
    knowledge_base = await knowledge_base_service.get_knowledge_base(kb_id)
    if not knowledge_base:
        raise HTTPException(status_code=404, detail="Knowledge base not found")
    
    # Check access
    if not knowledge_base.is_public and knowledge_base.created_by != current_user.username:
        raise HTTPException(status_code=403, detail="Access denied")
    
    try:
        stats = await vectorization_service.get_document_stats(kb_id)
        return KnowledgeBaseStats(**stats)
    except Exception as e:
        logger.error(f"Failed to get knowledge base stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/documents/{doc_id}/reprocess")
async def reprocess_document(
    doc_id: str,
    current_user: User = Depends(get_current_user)
):
    """重新处理文档。"""
    document = await document_service.get_document(doc_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Check access
    knowledge_base = await knowledge_base_service.get_knowledge_base(document.kb_id)
    if not knowledge_base:
        raise HTTPException(status_code=404, detail="Knowledge base not found")
    
    if not knowledge_base.is_public and knowledge_base.created_by != current_user.username:
        raise HTTPException(status_code=403, detail="Access denied")
    
    try:
        # Reset document status
        await document_service.update_document_status(doc_id, "pending")
        
        # Process document asynchronously
        asyncio.create_task(vectorization_service.process_document(doc_id))
        
        return {"message": "Document reprocessing started"}
    except Exception as e:
        logger.error(f"Failed to reprocess document: {e}")
        raise HTTPException(status_code=500, detail=str(e))
