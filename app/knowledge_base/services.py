"""
Knowledge base services for managing knowledge bases and documents.
"""

import os
import uuid
import hashlib
from pathlib import Path
from typing import List, Optional, Dict, Any
from datetime import datetime

from sqlalchemy import select, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession

from app.database.models import KnowledgeBase, Document, DocumentChunk
from app.database.connection import get_database_manager
from app.logger.logger import get_logger

logger = get_logger(__name__)


class KnowledgeBaseService:
    """Service for managing knowledge bases."""
    
    def __init__(self):
        self.upload_dir = Path("uploads")
        self.upload_dir.mkdir(exist_ok=True)
    
    async def create_knowledge_base(
        self,
        name: str,
        description: Optional[str] = None,
        created_by: str = "system",
        is_public: bool = False,
        config: Optional[Dict[str, Any]] = None
    ) -> KnowledgeBase:
        """Create a new knowledge base."""
        async with get_database_manager().get_session() as session:
            kb_id = str(uuid.uuid4())
            
            knowledge_base = KnowledgeBase(
                kb_id=kb_id,
                name=name,
                description=description,
                created_by=created_by,
                is_public=is_public,
                kb_config=config or {}
            )
            
            session.add(knowledge_base)
            await session.commit()
            await session.refresh(knowledge_base)
            
            logger.info(f"Created knowledge base: {kb_id} - {name}")
            return knowledge_base
    
    async def get_knowledge_base(self, kb_id: str) -> Optional[KnowledgeBase]:
        """Get knowledge base by ID."""
        async with get_database_manager().get_session() as session:
            result = await session.execute(
                select(KnowledgeBase).where(KnowledgeBase.kb_id == kb_id)
            )
            return result.scalar_one_or_none()
    
    async def list_knowledge_bases(
        self,
        created_by: Optional[str] = None,
        include_public: bool = True,
        limit: int = 100,
        offset: int = 0
    ) -> List[KnowledgeBase]:
        """List knowledge bases with filtering."""
        async with get_database_manager().get_session() as session:
            query = select(KnowledgeBase)
            
            conditions = []
            if created_by:
                if include_public:
                    conditions.append(
                        or_(
                            KnowledgeBase.created_by == created_by,
                            KnowledgeBase.is_public == True
                        )
                    )
                else:
                    conditions.append(KnowledgeBase.created_by == created_by)
            elif include_public:
                conditions.append(KnowledgeBase.is_public == True)
            
            if conditions:
                query = query.where(and_(*conditions))
            
            query = query.order_by(KnowledgeBase.created_at.desc())
            query = query.limit(limit).offset(offset)
            
            result = await session.execute(query)
            return list(result.scalars().all())
    
    async def update_knowledge_base(
        self,
        kb_id: str,
        name: Optional[str] = None,
        description: Optional[str] = None,
        is_public: Optional[bool] = None,
        config: Optional[Dict[str, Any]] = None
    ) -> Optional[KnowledgeBase]:
        """Update knowledge base."""
        async with get_database_manager().get_session() as session:
            result = await session.execute(
                select(KnowledgeBase).where(KnowledgeBase.kb_id == kb_id)
            )
            knowledge_base = result.scalar_one_or_none()
            
            if not knowledge_base:
                return None
            
            if name is not None:
                knowledge_base.name = name
            if description is not None:
                knowledge_base.description = description
            if is_public is not None:
                knowledge_base.is_public = is_public
            if config is not None:
                knowledge_base.kb_config = config
            
            knowledge_base.updated_at = datetime.utcnow()
            await session.commit()
            await session.refresh(knowledge_base)
            
            logger.info(f"Updated knowledge base: {kb_id}")
            return knowledge_base
    
    async def delete_knowledge_base(self, kb_id: str) -> bool:
        """Delete knowledge base and all associated documents."""
        async with get_database_manager().get_session() as session:
            # Get knowledge base
            result = await session.execute(
                select(KnowledgeBase).where(KnowledgeBase.kb_id == kb_id)
            )
            knowledge_base = result.scalar_one_or_none()
            
            if not knowledge_base:
                return False
            
            # Delete all document chunks
            await session.execute(
                DocumentChunk.__table__.delete().where(DocumentChunk.kb_id == kb_id)
            )
            
            # Delete all documents
            documents = await session.execute(
                select(Document).where(Document.kb_id == kb_id)
            )
            documents = list(documents.scalars().all())
            
            # Delete physical files
            for doc in documents:
                try:
                    file_path = Path(doc.file_path)
                    if file_path.exists():
                        file_path.unlink()
                except Exception as e:
                    logger.warning(f"Failed to delete file {doc.file_path}: {e}")
            
            await session.execute(
                Document.__table__.delete().where(Document.kb_id == kb_id)
            )
            
            # Delete knowledge base
            await session.delete(knowledge_base)
            await session.commit()
            
            logger.info(f"Deleted knowledge base: {kb_id}")
            return True


class DocumentService:
    """Service for managing documents."""
    
    def __init__(self):
        self.upload_dir = Path("uploads")
        self.upload_dir.mkdir(exist_ok=True)
    
    def _generate_file_hash(self, file_content: bytes) -> str:
        """Generate hash for file content."""
        return hashlib.sha256(file_content).hexdigest()
    
    def _get_file_extension(self, filename: str) -> str:
        """Get file extension."""
        return Path(filename).suffix.lower()
    
    async def upload_document(
        self,
        kb_id: str,
        filename: str,
        file_content: bytes,
        content_type: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Document:
        """Upload a document to knowledge base."""
        async with get_database_manager().get_session() as session:
            # Check if knowledge base exists
            kb_result = await session.execute(
                select(KnowledgeBase).where(KnowledgeBase.kb_id == kb_id)
            )
            if not kb_result.scalar_one_or_none():
                raise ValueError(f"Knowledge base {kb_id} not found")
            
            # Generate file hash
            content_hash = self._generate_file_hash(file_content)
            
            # Check for duplicate content
            existing_doc = await session.execute(
                select(Document).where(
                    and_(
                        Document.kb_id == kb_id,
                        Document.content_hash == content_hash
                    )
                )
            )
            if existing_doc.scalar_one_or_none():
                raise ValueError("Document with same content already exists")
            
            # Create unique filename
            doc_id = str(uuid.uuid4())
            file_extension = self._get_file_extension(filename)
            unique_filename = f"{doc_id}{file_extension}"
            
            # Create knowledge base directory
            kb_dir = self.upload_dir / kb_id
            kb_dir.mkdir(exist_ok=True)
            
            # Save file
            file_path = kb_dir / unique_filename
            with open(file_path, "wb") as f:
                f.write(file_content)
            
            # Create document record
            document = Document(
                doc_id=doc_id,
                kb_id=kb_id,
                filename=filename,
                file_path=str(file_path),
                file_size=len(file_content),
                file_type=content_type,
                content_hash=content_hash,
                status="pending",
                doc_metadata=metadata or {}
            )
            
            session.add(document)
            await session.commit()
            await session.refresh(document)
            
            logger.info(f"Uploaded document: {doc_id} - {filename}")
            return document
    
    async def get_document(self, doc_id: str) -> Optional[Document]:
        """Get document by ID."""
        async with get_database_manager().get_session() as session:
            result = await session.execute(
                select(Document).where(Document.doc_id == doc_id)
            )
            return result.scalar_one_or_none()
    
    async def list_documents(
        self,
        kb_id: str,
        status: Optional[str] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Document]:
        """List documents in knowledge base."""
        async with get_database_manager().get_session() as session:
            query = select(Document).where(Document.kb_id == kb_id)
            
            if status:
                query = query.where(Document.status == status)
            
            query = query.order_by(Document.created_at.desc())
            query = query.limit(limit).offset(offset)
            
            result = await session.execute(query)
            return list(result.scalars().all())
    
    async def update_document_status(
        self,
        doc_id: str,
        status: str,
        processing_error: Optional[str] = None,
        chunk_count: Optional[int] = None
    ) -> Optional[Document]:
        """Update document processing status."""
        async with get_database_manager().get_session() as session:
            result = await session.execute(
                select(Document).where(Document.doc_id == doc_id)
            )
            document = result.scalar_one_or_none()
            
            if not document:
                return None
            
            document.status = status
            if processing_error is not None:
                document.processing_error = processing_error
            if chunk_count is not None:
                document.chunk_count = chunk_count
            
            document.updated_at = datetime.utcnow()
            await session.commit()
            await session.refresh(document)
            
            logger.info(f"Updated document status: {doc_id} - {status}")
            return document
    
    async def delete_document(self, doc_id: str) -> bool:
        """Delete document and associated chunks."""
        async with get_database_manager().get_session() as session:
            result = await session.execute(
                select(Document).where(Document.doc_id == doc_id)
            )
            document = result.scalar_one_or_none()
            
            if not document:
                return False
            
            # Delete document chunks
            await session.execute(
                DocumentChunk.__table__.delete().where(DocumentChunk.doc_id == doc_id)
            )
            
            # Delete physical file
            try:
                file_path = Path(document.file_path)
                if file_path.exists():
                    file_path.unlink()
            except Exception as e:
                logger.warning(f"Failed to delete file {document.file_path}: {e}")
            
            # Delete document record
            await session.delete(document)
            await session.commit()
            
            logger.info(f"Deleted document: {doc_id}")
            return True


# Global service instances
knowledge_base_service = KnowledgeBaseService()
document_service = DocumentService()
