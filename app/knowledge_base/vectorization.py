"""
Vectorization service for processing documents and storing in ChromaDB.
"""

import os
import uuid
from typing import List, Optional, Dict, Any, cast
from pathlib import Path

# import chromadb
# from chromadb.config import Settings
# from chromadb.api import ClientAPI as ChromaClient
from llama_index.core import Document as LlamaDocument
from llama_index.core.node_parser import SentenceSplitter
from llama_index.core.schema import TextNode
from llama_index.embeddings.openai_like import OpenAILikeEmbedding
from sqlalchemy import select, func, and_

from app.database.models import Document, DocumentChunk
from app.routers.dependencies import get_db_session
from app.knowledge_base.services import document_service
from app.logger.logger import get_logger
from app.config.config import get_config

logger = get_logger(__name__)


class VectorizationService:
    """Service for vectorizing documents and storing in ChromaDB."""
    
    def __init__(self):
        self.config = get_config()
        # self.chroma_client: Optional[ChromaClient] = None
        self.embedding_model = None
        self.text_splitter = None
        # self._init_chroma()
        # self._init_embedding()
    
    def _init_chroma(self):
        """Initialize ChromaDB client."""
        try:
            # Create chroma directory if it doesn't exist
            chroma_dir = Path("chroma_db")
            chroma_dir.mkdir(exist_ok=True)
            self.chroma_client = None
            # self.chroma_client = chromadb.PersistentClient(
            #     path=str(chroma_dir),
            #     settings=Settings(
            #         anonymized_telemetry=False,
            #         allow_reset=True
            #     )
            # )
            logger.info("Initialized ChromaDB client")
        except Exception as e:
            logger.error(f"Failed to initialize ChromaDB: {e}")
            raise
    
    def _init_embedding(self):
        """Initialize embedding model."""
        try:
            # Get API key with fallback
            api_key = self.config.llm.default_api_key or self.config.llm.openai_api_key
            if not api_key:
                raise ValueError("No API key found for embedding model")
            
            # Use OpenAI-like embedding (can be configured for different providers)
            self.embedding_model = OpenAILikeEmbedding(
                model_name="text-embedding-3-small",
                api_base=self.config.llm.default_api_base,
                api_key=api_key
            )
            
            # Initialize text splitter
            self.text_splitter = SentenceSplitter(
                chunk_size=1000,
                chunk_overlap=200
            )
            
            logger.info("Initialized embedding model")
        except Exception as e:
            logger.error(f"Failed to initialize embedding model: {e}")
            raise
    
    def _get_collection_name(self, kb_id: str) -> str:
        """Get ChromaDB collection name for knowledge base."""
        return f"kb_{kb_id.replace('-', '_')}"
    
    async def create_collection(self, kb_id: str) -> bool:
        """Create a collection for knowledge base in ChromaDB."""
        try:
            if not self.chroma_client:
                raise ValueError("ChromaDB client not initialized")
                
            collection_name = self._get_collection_name(kb_id)
            
            # Check if collection already exists
            try:
                self.chroma_client.get_collection(name=collection_name)
                logger.info(f"Collection {collection_name} already exists")
                return True
            except Exception:
                pass
            
            # Create new collection
            self.chroma_client.create_collection(
                name=collection_name,
                metadata={"kb_id": kb_id}
            )
            
            logger.info(f"Created collection: {collection_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to create collection for {kb_id}: {e}")
            return False
    
    async def delete_collection(self, kb_id: str) -> bool:
        """Delete collection for knowledge base."""
        try:
            if not self.chroma_client:
                raise ValueError("ChromaDB client not initialized")
                
            collection_name = self._get_collection_name(kb_id)
            self.chroma_client.delete_collection(name=collection_name)
            logger.info(f"Deleted collection: {collection_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete collection for {kb_id}: {e}")
            return False
    
    async def process_document(self, doc_id: str) -> bool:
        """Process a document and store vectors in ChromaDB."""
        try:
            if not self.chroma_client or not self.text_splitter:
                raise ValueError("Required services not initialized")
                
            # Get document
            document = await document_service.get_document(doc_id)
            if not document:
                logger.error(f"Document {doc_id} not found")
                return False
            
            # Update status to processing
            await document_service.update_document_status(doc_id, "processing")
            
            # Create collection if it doesn't exist
            await self.create_collection(document.kb_id)
            
            # Read file content
            file_path = Path(document.file_path)
            if not file_path.exists():
                await document_service.update_document_status(
                    doc_id, "failed", "File not found"
                )
                return False
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            except UnicodeDecodeError:
                # Try different encoding for binary files
                with open(file_path, 'r', encoding='gb2312') as f:
                    content = f.read()
            except Exception as e:
                await document_service.update_document_status(
                    doc_id, "failed", f"Failed to read file: {str(e)}"
                )
                return False
            
            # Create LlamaDocument
            llama_doc = LlamaDocument(
                text=content,
                metadata={
                    "doc_id": doc_id,
                    "kb_id": document.kb_id,
                    "filename": document.filename,
                    "file_type": document.file_type
                }
            )
            
            # Split into chunks
            chunks = self.text_splitter.get_nodes_from_documents([llama_doc])
            
            if not chunks:
                await document_service.update_document_status(
                    doc_id, "failed", "No chunks generated"
                )
                return False
            
            # Get collection
            collection_name = self._get_collection_name(document.kb_id)
            collection = self.chroma_client.get_collection(name=collection_name)
            
            # Prepare vectors
            documents = []
            metadatas = []
            ids = []
            
            # Delete existing chunks for this document
            await self._delete_document_chunks(doc_id)
            
            # Process each chunk
            for i, chunk in enumerate(chunks):
                chunk_id = str(uuid.uuid4())
                if isinstance(chunk, TextNode):
                    chunk_content = chunk.text or ""
                else:
                    chunk_content = str(getattr(chunk, 'text', ''))
                
                # Store chunk in database
                await self._store_chunk(
                    chunk_id=chunk_id,
                    doc_id=doc_id,
                    kb_id=document.kb_id,
                    chunk_index=i,
                    content=chunk_content,
                    token_count=len(chunk_content.split())
                )
                
                # Prepare for ChromaDB
                documents.append(chunk_content)
                metadatas.append({
                    "doc_id": doc_id,
                    "kb_id": document.kb_id,
                    "chunk_index": i,
                    "chunk_id": chunk_id
                })
                ids.append(chunk_id)
            
            # Add to ChromaDB
            if documents:
                collection.add(
                    documents=documents,
                    metadatas=metadatas,
                    ids=ids
                )
                
                # Update document status
                await document_service.update_document_status(
                    doc_id, "completed", chunk_count=len(chunks)
                )
                
                logger.info(f"Processed document {doc_id} into {len(chunks)} chunks")
                return True
            else:
                await document_service.update_document_status(
                    doc_id, "failed", "No valid chunks to process"
                )
                return False
                
        except Exception as e:
            logger.error(f"Failed to process document {doc_id}: {e}")
            await document_service.update_document_status(
                doc_id, "failed", str(e)
            )
            return False
    
    async def _store_chunk(
        self,
        chunk_id: str,
        doc_id: str,
        kb_id: str,
        chunk_index: int,
        content: str,
        token_count: int
    ):
        """Store chunk in database."""
        session = await get_db_session().__anext__()
        try:
            chunk = DocumentChunk(
                chunk_id=chunk_id,
                doc_id=doc_id,
                kb_id=kb_id,
                chunk_index=chunk_index,
                content=content,
                token_count=token_count,
                vector_id=chunk_id
            )
            session.add(chunk)
            await session.commit()
        finally:
            await session.close()
    
    async def _delete_document_chunks(self, doc_id: str):
        """Delete all chunks for a document."""
        session = await get_db_session().__anext__()
        try:
            # Get chunks
            result = await session.execute(
                select(DocumentChunk).where(DocumentChunk.doc_id == doc_id)
            )
            chunks = result.scalars().all()
            
            if chunks:
                # Delete from ChromaDB
                kb_id = chunks[0].kb_id
                collection_name = self._get_collection_name(kb_id)
                try:
                    if self.chroma_client:
                        collection = self.chroma_client.get_collection(name=collection_name)
                        chunk_ids = [chunk.chunk_id for chunk in chunks]
                        collection.delete(ids=chunk_ids)
                except Exception as e:
                    logger.warning(f"Failed to delete chunks from ChromaDB: {e}")
                
                # Delete from database
                for chunk in chunks:
                    await session.delete(chunk)
                
                await session.commit()
        finally:
            await session.close()
    
    async def search_similar_chunks(
        self,
        kb_id: str,
        query: str,
        limit: int = 10,
        doc_ids: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """Search for similar chunks in knowledge base."""
        try:
            if not self.chroma_client:
                raise ValueError("ChromaDB client not initialized")
                
            collection_name = self._get_collection_name(kb_id)
            
            try:
                collection = self.chroma_client.get_collection(name=collection_name)
            except Exception:
                logger.warning(f"Collection {collection_name} not found")
                return []
            
            # Build where clause - use simple dict for ChromaDB
            where_clause: Dict[str, Any] = {"kb_id": kb_id}
            if doc_ids:
                where_clause["doc_id"] = {"$in": doc_ids}
            
            # Query ChromaDB
            results = collection.query(
                query_texts=[query],
                n_results=min(limit, 100),
                where=where_clause
            )
            
            if not results:
                return []
            
            # Safe access to results with null checks
            documents_list = results.get('documents')
            metadatas_list = results.get('metadatas')
            distances_list = results.get('distances')
            ids_list = results.get('ids')
            
            if not documents_list or not documents_list[0]:
                return []
            
            # Format results with safe access
            formatted_results = []
            documents = documents_list[0] or []
            metadatas = metadatas_list[0] or [] if metadatas_list else []
            distances = distances_list[0] or [] if distances_list else []
            ids = ids_list[0] or [] if ids_list else []
            
            # Ensure all lists have the same length
            min_length = min(len(documents), len(metadatas), len(distances), len(ids))
            
            for i in range(min_length):
                formatted_results.append({
                    "content": documents[i],
                    "metadata": metadatas[i] if i < len(metadatas) else {},
                    "distance": distances[i] if i < len(distances) else 0.0,
                    "id": ids[i] if i < len(ids) else ""
                })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"Failed to search chunks: {e}")
            return []
    
    async def get_document_stats(self, kb_id: str) -> Dict[str, Any]:
        """Get statistics for knowledge base."""
        try:
            collection_name = self._get_collection_name(kb_id)
            
            try:
                if not self.chroma_client:
                    raise ValueError("ChromaDB client not initialized")
                collection = self.chroma_client.get_collection(name=collection_name)
                count = collection.count()
            except Exception:
                count = 0
            
            # Get document stats from database
            session = await get_db_session().__anext__()
            try:
                # Document stats
                doc_result = await session.execute(
                    select(func.count(Document.id))
                    .where(Document.kb_id == kb_id)
                )
                total_docs = doc_result.scalar()
                
                # Processed document stats
                processed_result = await session.execute(
                    select(func.count(Document.id))
                    .where(
                        and_(
                            Document.kb_id == kb_id,
                            Document.status == "completed"
                        )
                    )
                )
                processed_docs = processed_result.scalar()
                
                # Chunk stats
                chunk_result = await session.execute(
                    select(func.count(DocumentChunk.id))
                    .where(DocumentChunk.kb_id == kb_id)
                )
                total_chunks = chunk_result.scalar()
                
                return {
                    "total_documents": total_docs or 0,
                    "processed_documents": processed_docs or 0,
                    "total_chunks": total_chunks or 0,
                    "vector_count": count
                }
            finally:
                await session.close()
            
        except Exception as e:
            logger.error(f"Failed to get stats: {e}")
            return {
                "total_documents": 0,
                "processed_documents": 0,
                "total_chunks": 0,
                "vector_count": 0
            }


# Global service instance
vectorization_service = VectorizationService()
