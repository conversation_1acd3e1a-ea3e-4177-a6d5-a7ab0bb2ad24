"""
用户认证的API端点。
"""
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm

from app.auth import schemas, security, services

router = APIRouter()

@router.post("/users", response_model=schemas.User, summary="创建新用户")
async def create_user(user: schemas.UserCreate, current_user: schemas.User = Depends(security.get_current_internal_user)):
    """
    创建一个新用户。

    - **user**: 包含用户名和密码的用户创建数据。
    """
    try:
        user = await services.UserService.create_user(user)
        return schemas.User(username=user.username, id=str(user.id), is_active=True)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/token", response_model=schemas.Token, summary="用户登录获取Token")
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    """
    使用表单数据（用户名和密码）登录，成功后返回访问令牌。

    - **form_data**: 包含 `username` 和 `password` 的OAuth2密码请求表单。
    """
    user = await services.UserService.get_user_by_username(form_data.username)
    if not user or not security.verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="不正确的用户名或密码",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token = security.create_access_token(
        data={"sub": user.username}
    )
    return {"access_token": access_token, "token_type": "bearer"}

@router.get("/users/me", response_model=schemas.User, summary="获取当前用户信息")
async def read_users_me(current_user: schemas.User = Depends(security.get_current_user)):
    """
    获取当前认证用户的信息。需要有效的访问令牌。
    """
    return current_user

@router.get("/users/welcome", summary="欢迎接口")
async def welcome(current_user: Optional[schemas.User] = Depends(security.get_optional_current_user)):
    """
    一个可选认证的欢迎接口。
    - 如果用户提供了有效的令牌，返回欢迎用户的消息。
    - 如果用户未提供令牌或令牌无效，返回欢迎访客的消息。
    """
    if current_user:
        return {"message": f"你好, {current_user.username}!"}
    else:
        return {"message": "你好, 访客!"}

@router.get("/users", response_model=List[schemas.UserListItem], summary="获取系统用户列表")
async def get_users(current_user: schemas.User = Depends(security.get_current_internal_user)):
    """
    获取当前系统中所有用户的列表，包含用户名和创建时间。
    按创建时间倒序排列（最新创建的用户在前）。
    """
    users = await services.UserService.get_all_users()
    return [schemas.UserListItem(username=user.username, created_at=user.created_at) for user in users]

@router.get("/third-party-token", summary="获取三方系统管理员Token")
async def get_third_party_admin_token(current_user: schemas.User = Depends(security.get_current_internal_user)):
    """
    获取三方系统管理员Token。
    
    - 优先从Redis缓存中获取Token
    - 如果缓存中没有，则调用第三方API获取
    - Token有效期为1天，自动缓存到Redis
    """
    token = await services.third_party_token_service.get_admin_token()
    
    if not token:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="无法获取第三方系统管理员Token"
        )
    
    return {"token": token}
