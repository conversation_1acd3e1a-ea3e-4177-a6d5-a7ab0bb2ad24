"""
Pydantic schemas for user authentication.
"""
from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel

class UserBase(BaseModel):
    username: Optional[str]

class UserCreate(UserBase):
    password: str

class User(UserBase):
    id: str
    is_active: bool

    class Config:
        from_attributes = True

class UserListItem(BaseModel):
    username: str
    created_at: datetime

    class Config:
        from_attributes = True

class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: str | None = None
