"""
Security utilities for authentication.
"""
from datetime import datetime, timedelta, timezone
from typing import Optional
import json
import redis.asyncio as redis

from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import Depends, HTTPException, status, Header
from fastapi.security import OAuth2<PERSON>asswordBearer

from app.auth import schemas
from app.config import get_config

from app.auth.services import UserService

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/auth/token")
optional_oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/auth/token", auto_error=False)

config = get_config()
auth_config = config.auth
redis_client = redis.from_url(config.redis_url, encoding="utf-8", decode_responses=True)

def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=auth_config.access_token_expire_minutes)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, auth_config.secret_key, algorithm=auth_config.algorithm)
    return encoded_jwt

async def get_current_user(
    token: Optional[str] = Depends(optional_oauth2_scheme),
    x_dt_accesstoken: Optional[str] = Header(None, alias="X-DT-accessToken"),
    x_dt_clientid: Optional[str] = Header(None, alias="x-dt-clientid"),
    x_dt_base_url: Optional[str] = Header(None, alias="x-dt-base-url"),
):
    if x_dt_accesstoken and x_dt_clientid:
        user = await verify_third_party_token(x_dt_accesstoken, x_dt_clientid, x_dt_base_url)
        if user:
            return user

    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    if not token:
        raise credentials_exception

    try:
        payload = jwt.decode(
            token, auth_config.secret_key, algorithms=[auth_config.algorithm]
        )
        username: Optional[str] = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = schemas.TokenData(username=username)
    except JWTError:
        raise credentials_exception

    if token_data.username is None:
        raise credentials_exception

    # Try to get user from cache first
    cache_key = f"easy-agent-center:user:{token_data.username}"
    try:
        cached_user = await redis_client.get(cache_key)
        if cached_user:
            user_data = json.loads(cached_user)
            return schemas.User(**user_data)
    except Exception:
        # Redis might be down, proceed to fetch from DB
        pass

    user = await UserService.get_user_by_username(username=token_data.username)
    if user is None:
        raise credentials_exception

    # Store user in cache for 5 minutes
    try:
        user_dict = {"id": str(user.id), "username": user.username, "is_active": user.is_active}
        await redis_client.set(cache_key, json.dumps(user_dict), ex=300)
    except Exception:
        # Redis might be down, but we can still return the user
        pass

    return schemas.User(**user_dict)


async def verify_third_party_token(access_token: str, client_id: str, base_url: Optional[str] = None):
    import httpx
    from app.auth.services import UserService

    cache_key = f"easy-agent-center:user_info:{client_id}:{access_token}"
    try:
        cached_user = await redis_client.get(cache_key)
        if cached_user:
            user_data = json.loads(cached_user)
            return schemas.User(**user_data)
    except Exception:
        # Redis might be down, proceed to fetch from source
        pass

    final_base_url = base_url or auth_config.third_party.base_url
    url = f"{final_base_url}/user-center/getUserInfo"
    headers = {"X-DT-accessToken": access_token, "x-dt-clientid": client_id}

    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(url, headers=headers)
            response.raise_for_status()
            user_data = response.json().get("data", {})
            
            userid = user_data.get("userId")
            username = user_data.get("name")
            if not userid:
                return None

            user = schemas.User(id=str(userid), username=username, is_active=True)
            
            # Store user in cache for 10h
            try:
                user_dict = {"id": user.id, "username": user.username, "is_active": user.is_active}
                await redis_client.set(cache_key, json.dumps(user_dict), ex=36000)
            except Exception:
                # Redis might be down, but we can still return the user
                pass
            return user
        except httpx.HTTPStatusError:
            return None
        except Exception:
            return None


async def get_current_internal_user(token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        payload = jwt.decode(
            token, auth_config.secret_key, algorithms=[auth_config.algorithm]
        )
        username: Optional[str] = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = schemas.TokenData(username=username)
    except JWTError:
        raise credentials_exception

    if token_data.username is None:
        raise credentials_exception

    # Try to get user from cache first
    cache_key = f"easy-agent-center:user:{token_data.username}"
    try:
        cached_user = await redis_client.get(cache_key)
        if cached_user:
            user_data = json.loads(cached_user)
            return schemas.User(**user_data)
    except Exception:
        # Redis might be down, proceed to fetch from DB
        pass

    user = await UserService.get_user_by_username(username=token_data.username)
    if user is None:
        raise credentials_exception

    # Store user in cache for 5 minutes
    try:
        user_dict = {"id": user.id, "username": user.username, "is_active": user.is_active}
        await redis_client.set(cache_key, json.dumps(user_dict), ex=300)
    except Exception:
        # Redis might be down, but we can still return the user
        pass

    return user


async def get_optional_current_user(
    token: Optional[str] = Depends(optional_oauth2_scheme),
    x_dt_accesstoken: Optional[str] = Header(None, alias="X-DT-accessToken"),
    x_dt_clientid: Optional[str] = Header(None, alias="x-dt-clientid"),
    x_dt_base_url: Optional[str] = Header(None, alias="x-dt-base-url"),
):
    if token is None:
        return None
    try:
        return await get_current_user(token, x_dt_accesstoken, x_dt_clientid, x_dt_base_url)
    except JWTError:
        return None
