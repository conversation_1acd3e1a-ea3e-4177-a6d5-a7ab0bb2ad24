"""
Database models for user authentication.
"""
from datetime import datetime
from sqlalchemy import Integer, String, DateTime, Boolean
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy.sql import func
from app.database.models import Base

class User(Base):
    """User model for storing user credentials."""
    
    __tablename__ = "users"
    __table_args__ = {'comment': '用户表'}
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment='主键 ID')
    username: Mapped[str] = mapped_column(String(255), unique=True, nullable=False, index=True, comment='用户名')
    hashed_password: Mapped[str] = mapped_column(String(255), nullable=False, comment='哈希密码')
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, comment='是否激活')
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), nullable=False, comment='创建时间')
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment='更新时间')

    def __repr__(self):
        return f"<User(username='{self.username}')>"
