"""
智能体聊天的 FastAPI 路由。

该模块包含用于智能体聊天功能的所有 API 端点，
包括流式聊天、聊天历史管理和标题生成。
"""

from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
from typing import List, Optional
import json

from app.auth.schemas import User
from app.logger.logger import get_logger
from app.database.services import ChatService
from app.agents.agent_manager import agent_manager
from app.auth.security import get_current_user
from .models import (
    ChatRequest,
    ChatResponse,
    ChatHistoryResponse,
    ChatMessageResponse,
    GenerateTitleRequest
)

# 初始化路由器和日志记录器
router = APIRouter(prefix="/agents/chat", tags=["智能体聊天"], dependencies=[Depends(get_current_user)])
api_logger = get_logger("llm.chat_api")


# 聊天端点
@router.post("/stream", summary="与智能体发送消息并获得流式响应")
async def stream_chat_with_agent(request: ChatRequest, current_user: User = Depends(get_current_user)):
    """向智能体发送消息并获得流式响应。"""
    try:
        async def generate_response():
            async for chunk in agent_manager.stream_chat_with_agent(
                message=request.message,
                agent_id=request.agent_id,
                session_id=request.session_id,
                user_name=current_user.username,
                biz_u_id=current_user.id
            ):
                yield f"data: {json.dumps(chunk)}\n\n"

        return StreamingResponse(
            generate_response(),
            media_type="text/event-stream",
            headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理流式聊天时出错: {str(e)}")


@router.get("/history", response_model=List[ChatHistoryResponse], summary="列出聊天历史")
async def list_chat_histories(agent_id: Optional[str] = None, limit: int = 50, current_user: User = Depends(get_current_user)):
    """列出聊天历史。"""
    try:
        histories = await ChatService.list_chat_histories(agent_id=agent_id, limit=limit, user_id=current_user.id)
        return [ChatHistoryResponse(**history.to_dict()) for history in histories]
    except Exception as e:
        api_logger.error("列出聊天历史失败", exception=e)
        raise HTTPException(status_code=500, detail=f"列出聊天历史时出错: {str(e)}")


@router.get("/history/{session_id}", response_model=ChatHistoryResponse, summary="获取特定的聊天历史")
async def get_chat_history(session_id: str):
    """获取一个特定的聊天历史。"""
    try:
        history = await ChatService.get_chat_history(session_id)
        if not history:
            raise HTTPException(status_code=404, detail=f"未找到聊天历史: {session_id}")
        return ChatHistoryResponse(**history.to_dict())
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"获取聊天历史 {session_id} 失败", exception=e)
        raise HTTPException(status_code=500, detail=f"获取聊天历史时出错: {str(e)}")


@router.delete("/history/{session_id}", summary="逻辑删除会话（软删除）")
async def delete_chat_history(session_id: str):
    try:
        deleted = await ChatService.delete_chat_history(session_id)
        if not deleted:
            raise HTTPException(status_code=404, detail=f"未找到聊天历史: {session_id}")
        return {"success": True}
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"逻辑删除聊天历史 {session_id} 失败", exception=e)
        raise HTTPException(status_code=500, detail=f"逻辑删除聊天历史时出错: {str(e)}")


@router.get("/history/{session_id}/messages", response_model=List[ChatMessageResponse], summary="获取聊天会话的消息")
async def get_chat_messages(session_id: str, limit: int = 100):
    """获取一个聊天会话的消息。"""
    try:
        messages = await ChatService.get_messages(session_id, limit=limit)
        return [ChatMessageResponse(**message.to_dict()) for message in messages]
    except Exception as e:
        api_logger.error(f"获取会话 {session_id} 的消息失败", exception=e)
        raise HTTPException(status_code=500, detail=f"获取消息时出错: {str(e)}")


@router.post("/generate-title", summary="为对话生成标题")
async def generate_title(request: GenerateTitleRequest):
    """为指定的对话生成一个简洁的标题。"""
    try:
        title = await agent_manager.generate_title(
            session_id=request.session_id,
            agent_id=request.agent_id
        )
        return {"title": title}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        api_logger.error(f"为会话 {request.session_id} 生成标题失败", exception=e)
        raise HTTPException(status_code=500, detail=f"生成标题时出错: {str(e)}")