"""
智能体管理的 FastAPI 路由。

该模块包含用于管理智能体的所有 API 端点，
包括 CRUD 操作、聊天功能和智能体配置。
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Any

from app.logger.logger import get_logger, PerformanceLogger
from app.database.services import AgentService
from app.agents.agent_manager import agent_manager
from app.auth.security import get_current_internal_user
from .models import (
    CreateAgentRequest,
    UpdateAgentRequest,
    AgentDeleteResponse,
    AgentSetDefaultResponse,
    AgentSafeResponse
)

# 初始化路由器和日志记录器
router = APIRouter(prefix="/agents", tags=["智能体管理"], dependencies=[Depends(get_current_internal_user)])
api_logger = get_logger("llm.agent_api")


# 智能体管理端点
@router.get("", response_model=List[AgentSafeResponse], summary="列出所有可用的智能体")
async def list_agents():
    """列出所有可用的智能体。"""
    try:
        agents = await AgentService.list_agents()
        from sqlalchemy import select
        from sqlalchemy.ext.asyncio import AsyncSession
        from app.database.connection import get_database_manager
        from app.database.models import AgentConfig
        db_manager = get_database_manager()
        results: List[AgentSafeResponse] = []
        async with db_manager.get_session() as session:
            for agent in agents:
                cfg = await session.execute(select(AgentConfig).where(AgentConfig.agent_id == agent.agent_id))
                cfg_row = cfg.scalars().first()
                agent_dict = agent.to_dict()
                agent_dict["agent_config"] = cfg_row.to_dict() if cfg_row else {}
                results.append(AgentSafeResponse(**agent_dict))
        return results
    except Exception as e:
        api_logger.error("从数据库列出智能体失败", exception=e)
        raise HTTPException(status_code=500, detail=f"从数据库列出智能体时出错: {str(e)}")


@router.post("", response_model=Dict[str, Any], summary="创建一个新的智能体")
async def create_agent(request: CreateAgentRequest):
    """创建一个新的智能体。"""
    try:
        with PerformanceLogger(f"api_create_agent_{request.agent_id}", api_logger):
            agent = await agent_manager.create_agent(
                agent_id=request.agent_id,
                agent_type=request.agent_type,
                name=request.name,
                description=request.description,
                system_prompt=request.system_prompt,
                set_as_default=request.set_as_default,
                provider=request.provider,
                model=request.model,
                llm_config=request.llm_config,
                mcp_config=request.mcp_config
            )

            api_logger.info(f"✅ 智能体创建成功: {request.agent_id}")

            # 从数据库获取新创建的智能体完整信息
            new_agent = await AgentService.get_agent_by_id(request.agent_id)
            if not new_agent:
                raise HTTPException(status_code=404, detail=f"创建后无法找到智能体: {request.agent_id}")

            return {
                "message": f"智能体 '{request.agent_id}' 创建成功",
                "agent": AgentSafeResponse(**new_agent.to_dict())
            }
    except ValueError as e:
        api_logger.warning(f"智能体创建失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        api_logger.error(f"为 {request.agent_id} 创建智能体失败", exception=e)
        raise HTTPException(status_code=500, detail=f"创建智能体时出错: {str(e)}")


@router.get("/{agent_id}", response_model=AgentSafeResponse, summary="获取特定智能体的信息")
async def get_agent(agent_id: str):
    """获取特定智能体的信息。"""
    try:
        agent = await AgentService.get_agent_by_id(agent_id)
        if not agent:
            raise HTTPException(status_code=404, detail=f"在数据库中未找到智能体: {agent_id}")
        from sqlalchemy import select
        from app.database.connection import get_database_manager
        from app.database.models import AgentConfig
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            res = await session.execute(select(AgentConfig).where(AgentConfig.agent_id == agent.agent_id))
            cfg = res.scalars().first()
        agent_dict = agent.to_dict()
        agent_dict["agent_config"] = cfg.to_dict() if cfg else {}
        return AgentSafeResponse(**agent_dict)
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"从数据库获取智能体失败: {agent_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"从数据库获取智能体时出错: {str(e)}")


@router.put("/{agent_id}", response_model=Dict[str, Any], summary="更新一个智能体")
async def update_agent(agent_id: str, request: UpdateAgentRequest):
    """更新一个智能体。"""
    try:
        with PerformanceLogger(f"api_update_agent_{agent_id}", api_logger):
            # 检查智能体是否存在
            existing_agent = await AgentService.get_agent_by_id(agent_id)
            if not existing_agent:
                raise HTTPException(status_code=404, detail=f"未找到智能体 '{agent_id}'")

            # 调用agent_manager的update_agent方法
            updated_agent_dict = await agent_manager.update_agent(
                agent_id=agent_id,
                name=request.name,
                description=request.description,
                system_prompt=request.system_prompt,
                set_as_default=request.set_as_default,
                provider=request.provider,
                model=request.model,
                llm_config=request.llm_config,
                mcp_config=request.mcp_config
            )
            
            if not updated_agent_dict:
                raise HTTPException(status_code=400, detail="没有有效的字段可更新")
            
            api_logger.info(f"✅ 智能体更新成功: {agent_id}")
            
            return {
                "message": f"智能体 '{agent_id}' 更新成功",
                "agent": AgentSafeResponse(**updated_agent_dict)
            }
                
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"更新智能体失败: {agent_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"更新智能体时出错: {str(e)}")


@router.delete("/{agent_id}", response_model=AgentDeleteResponse, summary="删除一个智能体")
async def delete_agent(agent_id: str):
    """删除一个智能体。"""
    try:
        if await agent_manager.remove_agent(agent_id):
            api_logger.info(f"已删除智能体: {agent_id}")
            return AgentDeleteResponse(message=f"智能体 '{agent_id}' 删除成功")
        else:
            raise HTTPException(status_code=404, detail=f"未找到智能体 '{agent_id}'")
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"删除智能体失败: {agent_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"删除智能体时出错: {str(e)}")


@router.post("/{agent_id}/reset", response_model=Dict[str, str], summary="重置智能体的对话历史")
async def reset_agent(agent_id: str):
    """重置智能体的对话历史。"""
    try:
        if await agent_manager.reset_agent(agent_id):
            api_logger.info(f"已重置智能体: {agent_id}")
            return {"message": f"智能体 '{agent_id}' 重置成功"}
        else:
            raise HTTPException(status_code=404, detail=f"未找到智能体 '{agent_id}'")
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"重置智能体失败: {agent_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"重置智能体时出错: {str(e)}")


@router.post("/{agent_id}/set-default", response_model=AgentSetDefaultResponse, summary="设置一个智能体为默认")
async def set_default_agent(agent_id: str):
    """设置一个智能体为默认。"""
    try:
        if await agent_manager.set_default_agent(agent_id):
            api_logger.info(f"已设置默认智能体: {agent_id}")
            return AgentSetDefaultResponse(message=f"智能体 '{agent_id}' 已成功设为默认")
        else:
            raise HTTPException(status_code=404, detail=f"未找到智能体 '{agent_id}'")
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"设置默认智能体失败: {agent_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"设置默认智能体时出错: {str(e)}")


