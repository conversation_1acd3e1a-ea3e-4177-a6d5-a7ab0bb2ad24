"""
Example tools for agents.
"""

import requests
from typing import Optional
from llama_index.core.tools import FunctionTool


def get_weather(city: str) -> str:
    """
    Get current weather information for a city.
    
    Args:
        city: The name of the city to get weather for
        
    Returns:
        str: Weather information for the city
    """
    # This is a mock implementation - in real use, you'd call a weather API
    return f"The weather in {city} is sunny with a temperature of 22°C."


def calculate(expression: str) -> str:
    """
    Calculate a mathematical expression.
    
    Args:
        expression: Mathematical expression to evaluate (e.g., "2 + 3 * 4")
        
    Returns:
        str: Result of the calculation
    """
    try:
        # Simple evaluation - in production, use a safer math parser
        result = eval(expression)
        return f"The result of {expression} is {result}"
    except Exception as e:
        return f"Error calculating {expression}: {str(e)}"


def search_web(query: str, num_results: int = 5) -> str:
    """
    Search the web for information.
    
    Args:
        query: Search query
        num_results: Number of results to return
        
    Returns:
        str: Search results
    """
    # This is a mock implementation - in real use, you'd call a search API
    return f"Search results for '{query}':\n1. Example result 1\n2. Example result 2\n3. Example result 3"


def get_current_time() -> str:
    """
    Get the current time.
    
    Returns:
        str: Current time
    """
    from datetime import datetime
    return f"Current time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"


# Create tool instances
weather_tool = FunctionTool.from_defaults(fn=get_weather)
calculator_tool = FunctionTool.from_defaults(fn=calculate)
web_search_tool = FunctionTool.from_defaults(fn=search_web)
time_tool = FunctionTool.from_defaults(fn=get_current_time)

# List of all available tools
AVAILABLE_TOOLS = [
    weather_tool,
    calculator_tool,
    web_search_tool,
    time_tool
]
