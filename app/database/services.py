"""
Database services for Easy Agent Center.
"""

from typing import List, Optional, Dict, Any
from sqlalchemy import select, update, delete
from sqlalchemy.ext.asyncio import AsyncSession

from .models import Agent, ChatHistory, ChatMessage, LLMProvider, MCPServer, AgentConfig
from .connection import get_database_manager
from app.agents.models import LLMConfig
from app.logger.logger import get_logger

logger = get_logger("database.services")


class AgentService:
    """Service for managing agents in the database."""
    
    @staticmethod
    async def create_agent(
        agent_id: str,
        name: str,
        agent_type: str = "openai",
        description: Optional[str] = None,
        system_prompt: Optional[str] = None,
        model: Optional[str] = None,
        provider: Optional[str] = None,
        is_default: bool = False,
        tools: Optional[List[str]] = None,
        config: Optional[Dict[str, Any]] = None,
        llm_config: Optional[LLMConfig] = None,
        mcp_config: Optional[List[Dict[str, Any]]] = None
    ) -> Agent:
        """Create a new agent in the database."""
        db_manager = get_database_manager()
        
        async with db_manager.get_session() as session:
            # Check if agent already exists
            existing_agent = await AgentService.get_agent_by_id(agent_id, session)
            if existing_agent:
                raise ValueError(f"Agent with ID '{agent_id}' already exists")
            
            # If this is set as default, unset other defaults
            if is_default:
                await AgentService._unset_all_defaults(session)
            
            llm_config_dict = llm_config.model_dump(exclude_none=True) if llm_config else {}

            # Create new agent
            agent = Agent(
                agent_id=agent_id,
                name=name,
                agent_type=agent_type,
                description=description,
                system_prompt=system_prompt,
                model=model,
                provider=provider,
                is_default=is_default,
                tools=tools,
                config=config,
                llm_config=llm_config_dict,
                mcp_config=mcp_config or []
            )
            
            session.add(agent)
            await session.commit()
            await session.refresh(agent)
            
            logger.info(f"Created agent in database: {agent_id}")
            return agent
    
    @staticmethod
    async def get_agent_by_id(agent_id: str, session: Optional[AsyncSession] = None) -> Optional[Agent]:
        """Get agent by ID."""
        if session:
            result = await session.execute(
                select(Agent).where(Agent.agent_id == agent_id, Agent.is_active == True)
            )
            return result.scalar_one_or_none()
        else:
            db_manager = get_database_manager()
            async with db_manager.get_session() as session:
                result = await session.execute(
                    select(Agent).where(Agent.agent_id == agent_id, Agent.is_active == True)
                )
                return result.scalar_one_or_none()
    
    @staticmethod
    async def get_default_agent() -> Optional[Agent]:
        """Get the default agent."""
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            result = await session.execute(
                select(Agent).where(Agent.is_default == True, Agent.is_active == True)
            )
            return result.scalar_one_or_none()
    
    @staticmethod
    async def list_agents() -> List[Agent]:
        """List all active agents."""
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            result = await session.execute(
                select(Agent).where(Agent.is_active == True).order_by(Agent.created_at.desc())
            )
            return list(result.scalars().all())
    
    @staticmethod
    async def list_public_agents() -> List[Agent]:
        """List all active public agents."""
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            result = await session.execute(
                select(Agent)
                .join(AgentConfig, Agent.agent_id == AgentConfig.agent_id)
                .where(
                    Agent.is_active == True,
                    AgentConfig.is_public == 'public'
                )
                .order_by(Agent.created_at.desc())
            )
            return list(result.scalars().all())
    
    @staticmethod
    async def list_partial_access_agents(user_id: Optional[str] = None) -> List[Agent]:
        """List all active partial agents that the user has access to."""
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            query = (
                select(Agent)
                .join(AgentConfig, Agent.agent_id == AgentConfig.agent_id)
                .where(
                    Agent.is_active == True,
                    AgentConfig.is_public == 'partial'
                )
            )
            
            # 如果提供了user_id，则过滤user_scope
            if user_id:
                from sqlalchemy import func
                query = query.where(
                    func.json_contains(AgentConfig.user_scope, f'"{user_id}"')
                )
            else:
                # 如果没有user_id，返回空列表
                return []
                
            query = query.order_by(Agent.created_at.desc())
            result = await session.execute(query)
            return list(result.scalars().all())
    
    @staticmethod
    async def update_agent(
        agent_id: str,
        **updates
    ) -> Optional[Agent]:
        """Update an agent."""
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            # If setting as default, unset other defaults first
            if updates.get('is_default'):
                await AgentService._unset_all_defaults(session)

            agent = await AgentService.get_agent_by_id(agent_id, session)
            if not agent:
                return None

            for key, value in updates.items():
                setattr(agent, key, value)

            await session.commit()
            await session.refresh(agent)
            logger.info(f"Updated agent in database: {agent_id}")
            
            return agent
    
    @staticmethod
    async def delete_agent(agent_id: str) -> bool:
        """Soft delete an agent."""
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            result = await session.execute(
                update(Agent)
                .where(Agent.agent_id == agent_id, Agent.is_active == True)
                .values(is_active=False)
            )
            
            if result.rowcount > 0:
                await session.commit()
                logger.info(f"Deleted agent from database: {agent_id}")
                return True
            
            return False
    
    @staticmethod
    async def set_default_agent(agent_id: str) -> bool:
        """Set an agent as default."""
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            # First check if agent exists
            agent = await AgentService.get_agent_by_id(agent_id, session)
            if not agent:
                return False
            
            # Unset all defaults
            await AgentService._unset_all_defaults(session)
            
            # Set this agent as default
            await session.execute(
                update(Agent)
                .where(Agent.agent_id == agent_id)
                .values(is_default=True)
            )
            
            await session.commit()
            logger.info(f"Set default agent: {agent_id}")
            return True
    
    @staticmethod
    async def _unset_all_defaults(session: AsyncSession):
        """Unset all default agents."""
        await session.execute(
            update(Agent).values(is_default=False)
        )


class ChatService:
    """Service for managing chat history in the database."""
    
    @staticmethod
    async def create_chat_history(session_id: str, agent_id: str, title: Optional[str] = None, user_name: Optional[str] = None, biz_u_id: Optional[str] = None) -> ChatHistory:
        """Create a new chat history."""
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            chat_history = ChatHistory(
                session_id=session_id,
                agent_id=agent_id,
                title=title,
                user_name=user_name or "访客",
                biz_u_id=biz_u_id
            )
            
            session.add(chat_history)
            await session.commit()
            await session.refresh(chat_history)
            
            logger.info(f"Created chat history: {session_id}")
            return chat_history
    
    @staticmethod
    async def add_message(
        session_id: str,
        role: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> ChatMessage:
        """Add a message to chat history."""
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            # Get or create chat history
            result = await session.execute(
                select(ChatHistory).where(ChatHistory.session_id == session_id, ChatHistory.invalid == 0)
            )
            chat_history = result.scalar_one_or_none()
            
            if not chat_history:
                raise ValueError(f"Chat history not found: {session_id}")
            
            # Create message
            message = ChatMessage(
                chat_history_id=chat_history.id,
                role=role,
                content=content,
                message_metadata=metadata
            )
            
            session.add(message)
            await session.commit()
            await session.refresh(message)
            
            return message
    
    @staticmethod
    async def get_chat_history(session_id: str) -> Optional[ChatHistory]:
        """Get chat history by session ID."""
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            result = await session.execute(
                select(ChatHistory).where(ChatHistory.session_id == session_id, ChatHistory.invalid == 0)
            )
            return result.scalar_one_or_none()
    
    @staticmethod
    async def get_messages(session_id: str, limit: int = 100) -> List[ChatMessage]:
        """Get messages for a chat session."""
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            result = await session.execute(
                select(ChatMessage)
                .join(ChatHistory, ChatMessage.chat_history_id == ChatHistory.id)
                .where(ChatHistory.session_id == session_id, ChatHistory.invalid == 0, ChatMessage.invalid == 0)
                .order_by(ChatMessage.created_at.asc())
                .limit(limit)
            )
            return list(result.scalars().all())
    
    @staticmethod
    async def list_chat_histories(user_id: str, agent_id: Optional[str] = None, limit: int = 50) -> List[ChatHistory]:
        """List chat histories."""
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            query = select(ChatHistory).where(ChatHistory.biz_u_id == user_id).where(ChatHistory.invalid == 0).order_by(ChatHistory.updated_at.desc()).limit(limit)
            if agent_id:
                query = query.where(ChatHistory.agent_id == agent_id)
                
            
            result = await session.execute(query)
            return list(result.scalars().all())

    @staticmethod
    async def update_chat_history(session_id: str, updates: Dict[str, Any]) -> Optional[ChatHistory]:
        """Update a chat history."""
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            result = await session.execute(
                select(ChatHistory).where(ChatHistory.session_id == session_id, ChatHistory.invalid == 0)
            )
            chat_history = result.scalar_one_or_none()
            
            if not chat_history:
                return None

            for key, value in updates.items():
                if hasattr(chat_history, key):
                    setattr(chat_history, key, value)

            await session.commit()
            await session.refresh(chat_history)
            logger.info(f"Updated chat history in database: {session_id}")
            
            return chat_history

    @staticmethod
    async def delete_chat_history(session_id: str) -> bool:
        """Soft delete a chat history and its messages by session ID."""
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            result = await session.execute(
                select(ChatHistory).where(ChatHistory.session_id == session_id, ChatHistory.invalid == 0)
            )
            chat_history = result.scalar_one_or_none()
            if not chat_history:
                return False

            await session.execute(
                update(ChatHistory).where(ChatHistory.id == chat_history.id).values(invalid=1)
            )
            await session.commit()
            logger.info(f"Soft deleted chat history: {session_id}")
            return True


class LLMService:
    """Service for managing LLM providers in the database."""

    @staticmethod
    async def create_llm_provider(
        provider_id: str,
        name: str,
        provider_type: str,
        description: Optional[str] = None,
        api_base: Optional[str] = None,
        api_key: Optional[str] = None,
        default_model: str = "gpt-3.5-turbo",
        context_window: int = 128000,
        max_tokens: Optional[int] = None,
        temperature: float = 0.7,
        timeout: int = 60,
        max_retries: int = 3,
        is_chat_model: bool = True,
        is_function_calling_model: bool = False,
        is_default: bool = False,
        extra_params: Optional[Dict[str, Any]] = None
    ) -> LLMProvider:
        """Create a new LLM provider in the database."""
        db_manager = get_database_manager()

        async with db_manager.get_session() as session:
            # Check if provider already exists
            existing_provider = await LLMService.get_llm_provider_by_id(provider_id, session)
            if existing_provider:
                raise ValueError(f"LLM provider with ID '{provider_id}' already exists")

            # If this is set as default, unset other defaults
            if is_default:
                await LLMService._unset_all_defaults(session)

            # Create new provider
            provider = LLMProvider(
                provider_id=provider_id,
                name=name,
                description=description,
                provider_type=provider_type,
                api_base=api_base,
                api_key=api_key,
                default_model=default_model,
                context_window=context_window,
                max_tokens=max_tokens,
                temperature=temperature,
                timeout=timeout,
                max_retries=max_retries,
                is_chat_model=is_chat_model,
                is_function_calling_model=is_function_calling_model,
                is_default=is_default,
                extra_params=extra_params
            )

            session.add(provider)
            await session.commit()
            await session.refresh(provider)

            logger.info(f"Created LLM provider in database: {provider_id}")
            return provider

    @staticmethod
    async def get_llm_provider_by_id(provider_id: str, session: Optional[AsyncSession] = None) -> Optional[LLMProvider]:
        """Get LLM provider by ID."""
        if session:
            result = await session.execute(
                select(LLMProvider).where(LLMProvider.provider_id == provider_id, LLMProvider.is_active == True)
            )
            return result.scalar_one_or_none()
        else:
            db_manager = get_database_manager()
            async with db_manager.get_session() as session:
                result = await session.execute(
                    select(LLMProvider).where(LLMProvider.provider_id == provider_id, LLMProvider.is_active == True)
                )
                return result.scalar_one_or_none()

    @staticmethod
    async def get_default_llm_provider() -> Optional[LLMProvider]:
        """Get the default LLM provider."""
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            result = await session.execute(
                select(LLMProvider).where(LLMProvider.is_default == True, LLMProvider.is_active == True)
            )
            return result.scalar_one_or_none()

    @staticmethod
    async def list_llm_providers() -> List[LLMProvider]:
        """List all active LLM providers."""
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            result = await session.execute(
                select(LLMProvider).where(LLMProvider.is_active == True).order_by(LLMProvider.created_at.desc())
            )
            return list(result.scalars().all())

    @staticmethod
    async def update_llm_provider(
        provider_id: str,
        **updates
    ) -> Optional[LLMProvider]:
        """Update an LLM provider."""
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            # If setting as default, unset other defaults first
            if updates.get('is_default'):
                await LLMService._unset_all_defaults(session)

            provider = await LLMService.get_llm_provider_by_id(provider_id, session)
            if not provider:
                return None

            for key, value in updates.items():
                setattr(provider, key, value)

            await session.commit()
            await session.refresh(provider)
            logger.info(f"Updated LLM provider in database: {provider_id}")

            return provider

    @staticmethod
    async def delete_llm_provider(provider_id: str) -> bool:
        """Soft delete an LLM provider."""
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            result = await session.execute(
                delete(LLMProvider)
                .where(LLMProvider.provider_id == provider_id,)
            )

            if result.rowcount > 0:
                await session.commit()
                logger.info(f"Deleted LLM provider from database: {provider_id}")
                return True

            return False

    @staticmethod
    async def set_default_llm_provider(provider_id: str) -> bool:
        """Set an LLM provider as default."""
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            # First check if provider exists
            provider = await LLMService.get_llm_provider_by_id(provider_id, session)
            if not provider:
                return False

            # Unset all defaults
            await LLMService._unset_all_defaults(session)

            # Set this provider as default
            await session.execute(
                update(LLMProvider)
                .where(LLMProvider.provider_id == provider_id)
                .values(is_default=True)
            )

            await session.commit()
            logger.info(f"Set default LLM provider: {provider_id}")
            return True

    @staticmethod
    async def _unset_all_defaults(session: AsyncSession):
        """Unset all default LLM providers."""
        await session.execute(
            update(LLMProvider).values(is_default=False)
        )


class MCPService:
    """Service class for MCP server database operations."""

    @staticmethod
    async def create_mcp_server(
        server_id: str,
        name: str,
        command: Optional[str] = None,
        description: Optional[str] = None,
        args: Optional[List[str]] = None,
        env: Optional[Dict[str, str]] = None,
        type: str = "sse",
        server_url: Optional[str] = None,
        is_active: bool = True,
        timeout: int = 30,
        max_retries: int = 3,
        extra_config: Optional[Dict[str, Any]] = None,
        session: Optional[AsyncSession] = None
    ) -> MCPServer:
        """Create a new MCP server in the database."""
        
        async def _create(session: AsyncSession) -> MCPServer:
            # Check if server already exists
            existing_server = await MCPService.get_mcp_server_by_name(name, session)
            if existing_server:
                raise ValueError(f"MCP server with Name '{name}' already exists")

            # Create new server
            server = MCPServer(
                server_id=server_id,
                name=name,
                description=description,
                command=command,
                args=args or [],
                env=env or {},
                type=type,
                server_url=server_url,
                is_active=is_active,
                timeout=timeout,
                max_retries=max_retries,
                extra_config=extra_config or {}
            )

            session.add(server)
            await session.flush()  # Use flush to get the ID without committing
            await session.refresh(server)

            logger.info(f"Created MCP server in database: {name}")
            return server

        if session:
            return await _create(session)
        else:
            db_manager = get_database_manager()
            async with db_manager.get_session() as new_session:
                server = await _create(new_session)
                await new_session.commit()
                return server

    @staticmethod
    async def get_mcp_server_by_id(server_id: str, session: Optional[AsyncSession] = None) -> Optional[MCPServer]:
        """Get an MCP server by ID."""
        if session:
            result = await session.execute(
                select(MCPServer).where(MCPServer.server_id == server_id, MCPServer.is_active == True)
            )
            return result.scalar_one_or_none()

        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            result = await session.execute(
                select(MCPServer).where(MCPServer.server_id == server_id, MCPServer.is_active == True)
            )
            return result.scalar_one_or_none()
        
    @staticmethod
    async def get_mcp_server_by_name(name: str, session: Optional[AsyncSession] = None) -> Optional[MCPServer]:
        """Get an MCP server by ID."""
        if session:
            result = await session.execute(
                select(MCPServer).where(MCPServer.name == name)
            )
            return result.scalar_one_or_none()

        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            result = await session.execute(
                select(MCPServer).where(MCPServer.name == name)
            )
            return result.scalar_one_or_none()

    @staticmethod
    async def list_mcp_servers(is_active: Optional[bool] = True) -> List[MCPServer]:
        """
        List MCP servers.

        Args:
            is_active: Set to True to list active servers, False for inactive, or None for all. 
                       Defaults to True.
        """
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            query = select(MCPServer).order_by(MCPServer.created_at.desc())
            if is_active is not None:
                query = query.where(MCPServer.is_active == is_active)
            
            result = await session.execute(query)
            return list(result.scalars().all())

    @staticmethod
    async def update_mcp_server(
        server_id: str,
        **updates
    ) -> Optional[MCPServer]:
        """Update an MCP server."""
        db_manager = get_database_manager()

        async with db_manager.get_session() as session:
            server = await MCPService.get_mcp_server_by_id(server_id, session)
            if not server:
                return None

            for key, value in updates.items():
                setattr(server, key, value)

            await session.commit()
            await session.refresh(server)
            logger.info(f"Updated MCP server in database: {server_id}")

            return server

    @staticmethod
    async def delete_mcp_server(server_id: str) -> bool:
        """Soft delete an MCP server."""
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            result = await session.execute(
                update(MCPServer)
                .where(MCPServer.server_id == server_id, MCPServer.is_active == True)
                .values(is_active=False)
            )

            if result.rowcount > 0:
                await session.commit()
                logger.info(f"Deleted MCP server from database: {server_id}")
                return True

            return False
