"""
Database models for Easy Agent Center.
"""

from typing import Dict, Any
from datetime import datetime

from sqlalchemy import Integer, String, Text, DateTime, Boolean, JSON, Float, Enum
from sqlalchemy.orm import Mapped, mapped_column, declarative_base
from sqlalchemy.sql import func

Base = declarative_base()


class Agent(Base):
    """Agent model for storing agent configurations."""
    
    __tablename__ = "agents"
    __table_args__ = {'comment': 'Agent 配置表'}
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment='主键 ID')
    agent_id: Mapped[str] = mapped_column(String(255), unique=True, nullable=False, index=True, comment='Agent 的唯一标识符')
    name: Mapped[str] = mapped_column(String(255), nullable=False, comment='Agent 名称')
    description: Mapped[str | None] = mapped_column(Text, nullable=True, comment='Agent 描述')
    agent_type: Mapped[str] = mapped_column(String(50), nullable=False, default="react", comment='Agent 类型，例如 react')
    system_prompt: Mapped[str | None] = mapped_column(Text, nullable=True, comment='系统提示')
    model: Mapped[str] = mapped_column(String(255), nullable=False, default="gpt-3.5-turbo", comment='模型名称')
    provider: Mapped[str | None] = mapped_column(String(255), nullable=True, comment='LLM 提供商 ID')
    is_default: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False, comment='是否为默认 Agent')
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, comment='是否激活')
    tools: Mapped[list | None] = mapped_column(JSON, nullable=True, comment='工具列表 (JSON 数组)')  # Store tools as JSON array
    config: Mapped[dict | None] = mapped_column(JSON, nullable=True, comment='其他配置 (JSON)')  # Store additional configuration
    llm_config: Mapped[dict | None] = mapped_column(JSON, nullable=True, default=dict, comment='LLM 的详细配置')
    mcp_config: Mapped[list | None] = mapped_column(JSON, nullable=True, default=list, comment='MCP 配置列表 (JSON 数组)')  # Store MCP server configurations
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), nullable=False, comment='创建时间')
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment='更新时间')
    
    # Note: Relationships removed to avoid foreign key constraints
    # Use service layer methods to query related chat_histories manually
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert agent to dictionary."""
        return {
            "id": self.id,
            "agent_id": self.agent_id,
            "name": self.name,
            "description": self.description,
            "agent_type": self.agent_type,
            "system_prompt": self.system_prompt,
            "model": self.model,
            "provider": self.provider,
            "is_default": self.is_default,
            "is_active": self.is_active,
            "tools": self.tools,
            "config": self.config,
            "llm_config": self.llm_config,
            "mcp_config": self.mcp_config or [],
            "created_at": self.created_at.isoformat() if self.created_at is not None else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at is not None else None
        }
    
    def __repr__(self):
        return f"<Agent(agent_id='{self.agent_id}', name='{self.name}')>"


class ChatHistory(Base):
    """Chat history model for storing conversation sessions."""
    
    __tablename__ = "chat_histories"
    __table_args__ = {'comment': '聊天会话历史表'}
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment='主键 ID')
    session_id: Mapped[str] = mapped_column(String(255), nullable=False, index=True, comment='会话的唯一标识符')
    agent_id: Mapped[str] = mapped_column(String(255), nullable=False, index=True, comment='关联的 Agent ID')
    user_name: Mapped[str] = mapped_column(String(255), nullable=False, default="访客", index=True, comment='用户标识')
    biz_u_id: Mapped[str | None] = mapped_column(String(255), nullable=True, index=True, comment='业务用户id')
    title: Mapped[str | None] = mapped_column(String(500), nullable=True, comment='可选的会话标题')  # Optional conversation title
    invalid: Mapped[int] = mapped_column(Integer, nullable=False, default=0, comment='删除标记：0 有效，1 已删除')
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), nullable=False, comment='创建时间')
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment='更新时间')
    
    # Note: Relationships removed to avoid foreign key constraints
    # Use service layer methods to query related agent and messages manually
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert chat history to dictionary."""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "agent_id": self.agent_id,
            "user_name": self.user_name,
            "biz_u_id": self.biz_u_id,
            "title": self.title,
            "invalid": self.invalid,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at is not None else None
        }
    
    def __repr__(self):
        return f"<ChatHistory(session_id='{self.session_id}', agent_id='{self.agent_id}', user_name='{self.user_name}', biz_u_id='{self.biz_u_id}', invalid={self.invalid})>"


class ChatMessage(Base):
    """Chat message model for storing individual messages."""
    
    __tablename__ = "chat_messages"
    __table_args__ = {'comment': '聊天消息表'}
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment='主键 ID')
    chat_history_id: Mapped[int] = mapped_column(Integer, nullable=False, index=True, comment='关联的聊天历史 ID')
    role: Mapped[str] = mapped_column(String(20), nullable=False, comment='角色 (user, assistant, system)')  # 'user', 'assistant', 'system'
    content: Mapped[str] = mapped_column(Text, nullable=False, comment='消息内容')
    message_metadata: Mapped[dict | None] = mapped_column(JSON, nullable=True, comment='其他消息元数据 (JSON)')  # Store additional message metadata
    invalid: Mapped[int] = mapped_column(Integer, nullable=False, default=0, comment='删除标记：0 有效，1 已删除')
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), nullable=False, comment='创建时间')
    
    # Note: Relationships removed to avoid foreign key constraints
    # Use service layer methods to query related chat_history manually
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert chat message to dictionary."""
        return {
            "id": self.id,
            "chat_history_id": self.chat_history_id,
            "role": self.role,
            "content": self.content,
            "metadata": self.message_metadata,
            "invalid": self.invalid,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None
        }
    
    def __repr__(self):
        return f"<ChatMessage(role='{self.role}', content='{self.content[:50]}...', invalid={self.invalid})>"


class LLMProvider(Base):
    """LLM Provider model for storing LLM provider configurations."""

    __tablename__ = "llm_providers"
    __table_args__ = {'comment': 'LLM 提供商配置表'}

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment='主键 ID')
    provider_id: Mapped[str] = mapped_column(String(255), unique=True, nullable=False, index=True, comment='提供商的唯一标识符')
    name: Mapped[str] = mapped_column(String(255), nullable=False, comment='提供商名称')
    description: Mapped[str | None] = mapped_column(Text, nullable=True, comment='提供商描述')
    provider_type: Mapped[str] = mapped_column(String(50), nullable=False, comment='提供商类型 (例如 openai, openai_compatible, azure_openai)')  # openai, openai_compatible, azure_openai, etc.
    api_base: Mapped[str | None] = mapped_column(String(500), nullable=True, comment='API 地址')
    api_key: Mapped[str | None] = mapped_column(String(500), nullable=True, comment='API 密钥')
    default_model: Mapped[str] = mapped_column(String(255), nullable=False, default="gpt-3.5-turbo", comment='默认模型')
    context_window: Mapped[int] = mapped_column(Integer, default=128000, nullable=False, comment='上下文窗口大小')
    max_tokens: Mapped[int | None] = mapped_column(Integer, nullable=True, comment='最大令牌数')
    temperature: Mapped[float] = mapped_column(Float, default=0.7, nullable=False, comment='温度')
    timeout: Mapped[int] = mapped_column(Integer, default=60, nullable=False, comment='超时时间 (秒)')
    max_retries: Mapped[int] = mapped_column(Integer, default=3, nullable=False, comment='最大重试次数')
    is_chat_model: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, comment='是否为聊天模型')
    is_function_calling_model: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False, comment='是否为函数调用模型')
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, comment='是否激活')
    is_default: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False, comment='是否为默认提供商')
    extra_params: Mapped[list | None] = mapped_column(JSON, nullable=True, default=list, comment='额外的提供商特定参数 (JSON)')  # Store additional provider-specific parameters
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), nullable=False, comment='创建时间')
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment='更新时间')

    def to_dict(self) -> Dict[str, Any]:
        """Convert LLM provider to dictionary."""
        return {
            "id": self.id,
            "provider_id": self.provider_id,
            "name": self.name,
            "description": self.description,
            "provider_type": self.provider_type,
            "api_base": self.api_base,
            "api_key": self.api_key,  # Note: In production, you might want to mask this
            "default_model": self.default_model,
            "context_window": self.context_window,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "is_chat_model": self.is_chat_model,
            "is_function_calling_model": self.is_function_calling_model,
            "is_active": self.is_active,
            "is_default": self.is_default,
            "extra_params": self.extra_params,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at is not None else None
        }

    def to_dict_safe(self) -> Dict[str, Any]:
        """Convert LLM provider to dictionary without sensitive information."""
        result = self.to_dict()
        # Mask sensitive information
        if result.get("api_key"):
            result["api_key"] = "***masked***"
        return result

    def __repr__(self):
        return f"<LLMProvider(provider_id='{self.provider_id}', name='{self.name}', type='{self.provider_type}')>"


class MCPServer(Base):
    """MCP Server configuration model."""
    __tablename__ = "mcp_servers"
    __table_args__ = {'comment': 'MCP 服务配置表'}

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, comment='主键 ID')
    server_id: Mapped[str] = mapped_column(String(255), nullable=False, index=True, comment='服务器的唯一标识符')
    name: Mapped[str] = mapped_column(String(255), nullable=False, index=True, comment='服务器名称')
    type: Mapped[str] = mapped_column(String(50), nullable=False, comment='服务器类型')
    description: Mapped[str | None] = mapped_column(Text, nullable=True, comment='服务器描述')
    server_url: Mapped[str | None] = mapped_column(String(500), nullable=True, comment='SSE 端点 URL')
    command: Mapped[str | None] = mapped_column(String(500), nullable=True, comment='启动命令')
    args: Mapped[list | None] = mapped_column(JSON, nullable=True, default=list, comment='命令参数 (JSON 列表)')
    env: Mapped[dict | None] = mapped_column(JSON, nullable=True, default=dict, comment='环境变量 (JSON 字典)')
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, comment='是否激活')
    timeout: Mapped[int] = mapped_column(Integer, default=30, nullable=False, comment='超时时间 (秒)')
    max_retries: Mapped[int] = mapped_column(Integer, default=3, nullable=False, comment='最大重试次数')
    extra_config: Mapped[dict | None] = mapped_column(JSON, nullable=True, comment='额外的配置 (JSON)')
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), nullable=False, comment='创建时间')
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment='更新时间')
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "id": self.id,
            "server_id": self.server_id,
            "name": self.name,
            "type": self.type,
            "description": self.description,
            "server_url": self.server_url,
            "command": self.command,
            "args": self.args or [],
            "env": self.env or {},
            "is_active": self.is_active,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "extra_config": self.extra_config,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at is not None else None
        }

    def to_dict_safe(self) -> Dict[str, Any]:
        """Convert to dictionary without sensitive information."""
        return {
            "id": self.id,
            "server_id": self.server_id,
            "name": self.name,
            "type": self.type,
            "description": self.description,
            "server_url": self.server_url,
            "command": self.command,
            "args": self.args or [],
            "env": self.env or {},
            "is_active": self.is_active,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "extra_config": self.extra_config,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at is not None else None
        }

    def __repr__(self):
        return f"<MCPServer(server_id='{self.server_id}', name='{self.name}', type='{self.type}', command='{self.command}')>"


class KnowledgeBase(Base):
    """Knowledge base model for storing different knowledge bases."""
    __tablename__ = "knowledge_bases"
    __table_args__ = {'comment': '知识库表'}

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment='主键 ID')
    kb_id: Mapped[str] = mapped_column(String(255), unique=True, nullable=False, index=True, comment='知识库的唯一标识符')
    name: Mapped[str] = mapped_column(String(255), nullable=False, comment='知识库名称')
    description: Mapped[str | None] = mapped_column(Text, nullable=True, comment='知识库描述')
    created_by: Mapped[str] = mapped_column(String(255), nullable=False, comment='创建者')
    is_public: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False, comment='是否为公共知识库')
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, comment='是否激活')
    kb_config: Mapped[dict | None] = mapped_column(JSON, nullable=True, comment='知识库配置 (JSON)')
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), nullable=False, comment='创建时间')
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment='更新时间')

    def to_dict(self) -> Dict[str, Any]:
        """Convert knowledge base to dictionary."""
        return {
            "id": self.id,
            "kb_id": self.kb_id,
            "name": self.name,
            "description": self.description,
            "created_by": self.created_by,
            "is_public": self.is_public,
            "is_active": self.is_active,
            "config": self.kb_config,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at is not None else None
        }

    def __repr__(self):
        return f"<KnowledgeBase(kb_id='{self.kb_id}', name='{self.name}', created_by='{self.created_by}')>"


class Document(Base):
    """Document model for storing uploaded documents."""
    __tablename__ = "documents"
    __table_args__ = {'comment': '文档表'}

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment='主键 ID')
    doc_id: Mapped[str] = mapped_column(String(255), unique=True, nullable=False, index=True, comment='文档的唯一标识符')
    kb_id: Mapped[str] = mapped_column(String(255), nullable=False, index=True, comment='所属知识库 ID')
    filename: Mapped[str] = mapped_column(String(500), nullable=False, comment='原始文件名')
    file_path: Mapped[str] = mapped_column(String(1000), nullable=False, comment='文件存储路径')
    file_size: Mapped[int] = mapped_column(Integer, nullable=False, default=0, comment='文件大小 (字节)')
    file_type: Mapped[str] = mapped_column(String(100), nullable=False, comment='文件类型')
    content_hash: Mapped[str] = mapped_column(String(64), nullable=False, comment='内容哈希值')
    status: Mapped[str] = mapped_column(String(50), nullable=False, default="pending", comment='处理状态')
    processing_error: Mapped[str | None] = mapped_column(Text, nullable=True, comment='处理错误信息')
    chunk_count: Mapped[int] = mapped_column(Integer, nullable=False, default=0, comment='分块数量')
    doc_metadata: Mapped[dict | None] = mapped_column(JSON, nullable=True, comment='文档元数据 (JSON)')
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), nullable=False, comment='创建时间')
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment='更新时间')

    def to_dict(self) -> Dict[str, Any]:
        """Convert document to dictionary."""
        return {
            "id": self.id,
            "doc_id": self.doc_id,
            "kb_id": self.kb_id,
            "filename": self.filename,
            "file_path": self.file_path,
            "file_size": self.file_size,
            "file_type": self.file_type,
            "content_hash": self.content_hash,
            "status": self.status,
            "processing_error": self.processing_error,
            "chunk_count": self.chunk_count,
            "metadata": self.doc_metadata,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at is not None else None
        }

    def __repr__(self):
        return f"<Document(doc_id='{self.doc_id}', filename='{self.filename}', kb_id='{self.kb_id}')>"


class AgentConfig(Base):
    __tablename__ = "agent_configs"
    __table_args__ = {'comment': 'Agent 配置扩展表'}

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment='主键 ID')
    agent_id: Mapped[str] = mapped_column(String(255), nullable=False, index=True, comment='关联的 Agent ID')
    icon: Mapped[str | None] = mapped_column(String(500), nullable=True, comment='Agent 图标 URL 或标识')
    is_public: Mapped[str] = mapped_column(
        Enum('public', 'private', 'partial', name='agent_visibility'),
        nullable=False,
        default='private',
        comment='公开范围：public 全公开，private 不公开，partial 半公开'
    )
    user_scope: Mapped[list | None] = mapped_column(JSON, nullable=True, comment='半公开时可见的用户ID列表 (JSON 数组)')
    is_pinned: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False, comment='是否置顶')
    pin_order: Mapped[int | None] = mapped_column(Integer, nullable=True, comment='置顶排序，值越小越靠前')
    chat_recommend: Mapped[list | None] = mapped_column(JSON, nullable=True, comment='对话推荐')
    extra: Mapped[dict | None] = mapped_column(JSON, nullable=True, comment='其他业务属性')
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), nullable=False, comment='创建时间')
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment='更新时间')

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "agent_id": self.agent_id,
            "icon": self.icon,
            "is_public": self.is_public,
            "user_scope": self.user_scope or [],
            "is_pinned": self.is_pinned,
            "pin_order": self.pin_order,
            "chat_recommend": self.chat_recommend or [],
            "extra": self.extra,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at is not None else None
        }

    def __repr__(self):
        return f"<AgentConfig(agent_id='{self.agent_id}', is_public='{self.is_public}', is_pinned={self.is_pinned}, pin_order={self.pin_order})>"


class DocumentChunk(Base):
    """Document chunk model for storing vectorized chunks."""
    __tablename__ = "document_chunks"
    __table_args__ = {'comment': '文档分块表'}

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment='主键 ID')
    chunk_id: Mapped[str] = mapped_column(String(255), unique=True, nullable=False, index=True, comment='分块的唯一标识符')
    doc_id: Mapped[str] = mapped_column(String(255), nullable=False, index=True, comment='所属文档 ID')
    kb_id: Mapped[str] = mapped_column(String(255), nullable=False, index=True, comment='所属知识库 ID')
    chunk_index: Mapped[int] = mapped_column(Integer, nullable=False, comment='分块索引')
    content: Mapped[str] = mapped_column(Text, nullable=False, comment='分块内容')
    token_count: Mapped[int] = mapped_column(Integer, nullable=False, default=0, comment='分块令牌数')
    vector_id: Mapped[str | None] = mapped_column(String(255), nullable=True, comment='向量存储 ID')
    chunk_metadata: Mapped[dict | None] = mapped_column(JSON, nullable=True, comment='分块元数据 (JSON)')
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), nullable=False, comment='创建时间')

    def to_dict(self) -> Dict[str, Any]:
        """Convert document chunk to dictionary."""
        return {
            "id": self.id,
            "chunk_id": self.chunk_id,
            "doc_id": self.doc_id,
            "kb_id": self.kb_id,
            "chunk_index": self.chunk_index,
            "content": self.content,
            "token_count": self.token_count,
            "vector_id": self.vector_id,
            "metadata": self.doc_metadata,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None
        }

    def __repr__(self):
        return f"<DocumentChunk(chunk_id='{self.chunk_id}', doc_id='{self.doc_id}', index={self.chunk_index})>"
