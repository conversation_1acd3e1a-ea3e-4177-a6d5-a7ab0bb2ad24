"""
Database connection management for MySQL integration.
"""

import asyncio
from contextlib import asynccontextmanager
from typing import Async<PERSON>enerator, Optional

from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

from .config import DatabaseConfig, get_database_config
from .models import Base
from app.logger.logger import get_logger

logger = get_logger("database")


class DatabaseManager:
    """Database connection and session manager."""
    
    def __init__(self, config: Optional[DatabaseConfig] = None):
        """Initialize database manager with configuration."""
        self.config = config or get_database_config()
        self._engine: Optional[AsyncEngine] = None
        self._session_factory: Optional[async_sessionmaker] = None
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize database engine and create/migrate tables."""
        if self._initialized:
            logger.warning("Database already initialized")
            return

        try:
            # Create database if it doesn't exist
            await self._create_database_if_not_exists()

            # Create async engine
            self._engine = create_async_engine(
                self.config.database_url,
                pool_size=self.config.pool_size,
                max_overflow=self.config.max_overflow,
                pool_timeout=self.config.pool_timeout,
                pool_recycle=self.config.pool_recycle,
                echo=self.config.echo,
                future=True
            )

            # Create session factory
            self._session_factory = async_sessionmaker(
                bind=self._engine,
                class_=AsyncSession,
                expire_on_commit=False
            )

            await self._run_migrations()


            self._initialized = True
            logger.info("Database initialized successfully")

        except Exception as e:
            logger.error("Failed to initialize database", exception=e)
            raise
    
    async def _create_database_if_not_exists(self) -> None:
        """Create database if it doesn't exist."""
        try:
            # Connect without specifying database
            base_url = self.config.sync_database_url.rsplit('/', 1)[0]
            engine = create_engine(base_url)
            
            with engine.connect() as conn:
                # Check if database exists
                result = conn.execute(
                    text(f"SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '{self.config.database}'")
                )
                
                if not result.fetchone():
                    # Create database
                    conn.execute(text(f"CREATE DATABASE {self.config.database} CHARACTER SET {self.config.charset}"))
                    conn.commit()
                    logger.info(f"Created database: {self.config.database}")
                else:
                    logger.info(f"Database already exists: {self.config.database}")
            
            engine.dispose()

        except Exception as e:
            logger.error("Failed to create database", exception=e)
            raise

    async def _run_migrations(self) -> None:
        """Run database migrations."""
        try:
            # Import here to avoid circular imports
            from .migrations import get_migration_manager

            migration_manager = get_migration_manager()

            # Check migration status
            status = migration_manager.check_migration_status()
            logger.info(f"Migration status: {status['message']}")

            # Auto-upgrade if needed
            if status["status"] in ["not_initialized", "pending_migrations"]:
                success = await asyncio.create_task(
                    asyncio.to_thread(migration_manager.auto_upgrade)
                )
                if success:
                    logger.info("Database migrations completed successfully")
                else:
                    logger.warning("Migration auto-upgrade failed, falling back to table creation")
            elif status["status"] == "no_migrations":
                logger.info("No migrations found, creating tables directly")
        except Exception as e:
            logger.warning(f"Migration failed, falling back to table creation: {e}")
    
    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get database session context manager."""
        if not self._initialized:
            await self.initialize()
            
        if not self._session_factory:
            raise RuntimeError("Database not initialized")
            
        async with self._session_factory() as session:
            try:
                yield session
            except Exception as e:
                await session.rollback()
                logger.error("Database session error", exception=e)
                raise
            finally:
                await session.close()
    
    async def close(self) -> None:
        """Close database connections."""
        if self._engine:
            await self._engine.dispose()
            logger.info("Database connections closed")
        self._initialized = False
    
    @property
    def is_initialized(self) -> bool:
        """Check if database is initialized."""
        return self._initialized


# Global database manager instance
_database_manager: Optional[DatabaseManager] = None


def get_database_manager() -> DatabaseManager:
    """Get global database manager instance."""
    global _database_manager
    if _database_manager is None:
        _database_manager = DatabaseManager()
    return _database_manager
