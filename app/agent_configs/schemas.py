from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any

class AgentConfigUpsertRequest(BaseModel):
    agent_id: str = Field(..., description="Agent 的唯一标识符")
    icon: Optional[str] = Field(None, description="Agent 图标 URL 或标识")
    is_public: Optional[str] = Field(
        None,
        pattern=r"^(public|private|partial)$",
        description="公开范围：public 全公开，private 不公开，partial 半公开",
    )
    user_scope: Optional[List[str]] = Field(None, description="半公开时可见的用户ID列表")
    is_pinned: Optional[bool] = Field(None, description="是否置顶")
    pin_order: Optional[int] = Field(None, description="置顶排序，值越小越靠前")
    chat_recommend: Optional[List[str]] = Field(None, description="对话推荐")
    extra: Optional[Dict[str, Any]] = Field(None, description="其他业务属性")

class AgentConfigResponse(BaseModel):
    id: int = Field(..., description="数据库主键ID")
    agent_id: str = Field(..., description="Agent 的唯一标识符")
    icon: Optional[str] = Field(None, description="Agent 图标 URL 或标识")
    is_public: str = Field(..., description="公开范围：public/private/partial")
    user_scope: Optional[List[str]] = Field(None, description="半公开时可见的用户ID列表")
    is_pinned: bool = Field(..., description="是否置顶")
    pin_order: Optional[int] = Field(None, description="置顶排序，值越小越靠前")
    chat_recommend: Optional[List[str]] = Field(None, description="对话推荐")
    extra: Optional[Dict[str, Any]] = Field(None, description="其他业务属性")
    created_at: str = Field(..., description="创建时间戳")
    updated_at: str = Field(..., description="最后更新时间戳")

    class Config:
        from_attributes = True