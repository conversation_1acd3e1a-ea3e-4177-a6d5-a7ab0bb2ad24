#!/usr/bin/env python3
"""
Configuration checker for Easy Agent Center.

This script validates the configuration and checks if all required services are available.
"""

import os
import sys
from pathlib import Path

# Load configuration
try:
    from config.config import get_config
    config = get_config()
    CONFIG_AVAILABLE = True
except ImportError:
    CONFIG_AVAILABLE = False


def check_env_file():
    """Check if .env file exists and is readable."""
    print("🔍 Checking .env file...")
    
    env_file = Path(".env")
    if env_file.exists():
        print("✅ .env file found")
        try:
            with open(env_file, 'r') as f:
                lines = f.readlines()
            print(f"   📄 Contains {len(lines)} lines")
            return True
        except Exception as e:
            print(f"❌ Error reading .env file: {e}")
            return False
    else:
        print("⚠️ .env file not found")
        print("💡 Copy .env.example to .env and configure it")
        return False


def check_database_config():
    """Check database configuration."""
    print("\n🗄️ Checking database configuration...")
    
    try:
        from database.config import get_database_config
        db_config = get_database_config()
        
        print(f"ℹ️ Database host: {db_config.host}:{db_config.port}")
        print(f"ℹ️ Database name: {db_config.database}")
        print(f"ℹ️ Database user: {db_config.username}")
        
        if db_config.password:
            print("✅ Database password configured")
        else:
            print("⚠️ Database password not set")
        
        # Try to test database connection
        try:
            import asyncio
            from database.connection import DatabaseManager
            
            async def test_db():
                db_manager = DatabaseManager(db_config)
                try:
                    await db_manager.initialize()
                    print("✅ Database connection successful")
                    await db_manager.close()
                    return True
                except Exception as e:
                    print(f"❌ Database connection failed: {e}")
                    return False
            
            result = asyncio.run(test_db())
            return result
            
        except ImportError:
            print("⚠️ Database dependencies not installed")
            print("   Run: poetry add aiomysql pymysql 'sqlalchemy[asyncio]'")
            return False
            
    except ImportError:
        print("❌ Database module not available")
        return False


def check_server_config():
    """Check server configuration."""
    print("\n🌐 Checking server configuration...")
    
    if CONFIG_AVAILABLE:
        server_config = config.server
        print(f"ℹ️ Server will run on: {server_config.host}:{server_config.port}")
        print(f"ℹ️ Log level: {server_config.log_level}")
        print(f"ℹ️ Reload mode: {server_config.reload}")
        print(f"ℹ️ Environment: {config.environment}")
    else:
        host = os.getenv("HOST", "0.0.0.0")
        port = os.getenv("PORT", "8000")
        log_level = os.getenv("LOG_LEVEL", "INFO")
        environment = os.getenv("ENVIRONMENT", "development")
        
        print(f"ℹ️ Server will run on: {host}:{port}")
        print(f"ℹ️ Log level: {log_level}")
        print(f"ℹ️ Environment: {environment}")


def check_dependencies():
    """Check required dependencies."""
    print("\n📦 Checking dependencies...")
    
    required_packages = [
        ("fastapi", "FastAPI web framework"),
        ("uvicorn", "ASGI server"),
        ("llama-index", "LlamaIndex framework"),
        ("pydantic", "Data validation"),
    ]
    
    optional_packages = [
        ("aiomysql", "MySQL async driver"),
        ("pymysql", "MySQL sync driver"),
        ("sqlalchemy", "SQL toolkit"),
        ("python-dotenv", "Environment file support"),
    ]
    
    all_good = True
    
    for package, description in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package} - {description}")
        except ImportError:
            print(f"❌ {package} - {description} (REQUIRED)")
            all_good = False
    
    for package, description in optional_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package} - {description}")
        except ImportError:
            print(f"⚠️ {package} - {description} (optional)")
    
    return all_good


def check_file_permissions():
    """Check file permissions."""
    print("\n📁 Checking file permissions...")
    
    files_to_check = [
        "main.py",
        "run_server.py",
        "config.py",
        ".env.example"
    ]
    
    for file_path in files_to_check:
        path = Path(file_path)
        if path.exists():
            if os.access(path, os.R_OK):
                print(f"✅ {file_path} - readable")
            else:
                print(f"❌ {file_path} - not readable")
        else:
            print(f"⚠️ {file_path} - not found")


def main():
    """Run all configuration checks."""
    print("🚀 Easy Agent Center Configuration Checker")
    print("=" * 50)
    
    checks = [
        ("Environment file", check_env_file),
        ("Dependencies", check_dependencies),
        ("Database configuration", check_database_config),
        ("Server configuration", check_server_config),
        ("File permissions", check_file_permissions),
    ]
    
    results = []
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ Error in {check_name}: {e}")
            results.append((check_name, False))
    
    # Summary
    print("\n📊 Configuration Check Summary")
    print("=" * 30)
    
    passed = 0
    total = len(results)
    
    for check_name, result in results:
        if result is True:
            print(f"✅ {check_name}")
            passed += 1
        elif result is False:
            print(f"❌ {check_name}")
        else:
            print(f"⚠️ {check_name}")
    
    print(f"\n📈 Passed: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 All checks passed! You're ready to start the server.")
        print("   Run: python run_server.py")
    else:
        print("\n⚠️ Some checks failed. Please review the issues above.")
        print("💡 Check the README.md for configuration instructions.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
