# Easy Agent Center Configuration
# Copy this file to .env and update the values as needed

# =============================================================================
# LLM Configuration
# =============================================================================

# OpenAI API Key (required for OpenAI agents)
OPENAI_API_KEY=af78ce47-20be-4fa6-a47a-d4e64323b9f0

# Default LLM Configuration (optional)
# These will be used if no specific LLM is provided when creating agents
DEFAULT_API_BASE=https://ark.cn-beijing.volces.com/api/v3/
DEFAULT_API_KEY=af78ce47-20be-4fa6-a47a-d4e64323b9f0
DEFAULT_MODEL=deepseek-v3-250324

# =============================================================================
# MySQL Database Configuration (Optional)
# =============================================================================

# Database connection settings
DB_HOST=mysql.3ren.inside.srt
DB_PORT=8066
DB_USERNAME=mycat
DB_PASSWORD=Aa@123FgAPoO
DB_DATABASE=easy_agent_center
ALEMBIC_DB_USERNAME=mycat
ALEMBIC_DB_PASSWORD=Aa@123FgAPoO

# Database connection pool settings
DB_CHARSET=utf8mb4
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# Debug settings
DB_ECHO=true

# =============================================================================
# Redis Configuration
# =============================================================================

# Redis connection settings
REDIS_URL=redis://default:<EMAIL>:6379/0
REDIS_CACHE_TTL=3600

# =============================================================================
# MCP Configuration
# =============================================================================

# MCP synchronization interval in seconds (default: 60)
MCP_SYNC_INTERVAL=60

# =============================================================================
# Application Configuration
# =============================================================================

# Server settings
HOST=0.0.0.0
PORT=8000

# CORS settings (comma-separated origins, or "*" for all)
CORS_ORIGINS=*

# Logging configuration
LOG_LEVEL=INFO
LOG_TO_FILE=true
LOG_DIR=logs

# =============================================================================
# Development/Testing Configuration
# =============================================================================

# Environment (development, production, testing)
ENVIRONMENT=development

# Test database (used for running tests)
TEST_DB_DATABASE=test_easy_agent_center
SECRET_KEY=bf36859860de49c9c5d627186644cae2726012384324ff38e5e14927d17f1aa3
