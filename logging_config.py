"""
Logging configuration for Easy Agent Center.

This file provides centralized logging configuration that can be imported
and used throughout the application.
"""

import logging
import sys
from pathlib import Path
from datetime import datetime
from typing import Optional


def configure_logging(
    level: int = logging.INFO,
    log_to_file: bool = True,
    log_dir: str = "logs",
    console_format: Optional[str] = None,
    file_format: Optional[str] = None
):
    """
    Configure logging for the entire application.
    
    Args:
        level: Logging level (default: INFO)
        log_to_file: Whether to log to files (default: True)
        log_dir: Directory for log files (default: "logs")
        console_format: Custom console format string
        file_format: Custom file format string
    """
    # Create log directory
    if log_to_file:
        Path(log_dir).mkdir(exist_ok=True)
    
    # Default formats
    if console_format is None:
        console_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    if file_format is None:
        file_format = '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(level)
    console_formatter = logging.Formatter(
        console_format,
        datefmt='%H:%M:%S'
    )
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # File handlers
    if log_to_file:
        today = datetime.now().strftime('%Y%m%d')
        
        # Main log file
        file_handler = logging.FileHandler(
            Path(log_dir) / f"agents_{today}.log"
        )
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(
            file_format,
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
        
        # Error log file (errors and above only)
        error_handler = logging.FileHandler(
            Path(log_dir) / f"errors_{today}.log"
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(file_formatter)
        root_logger.addHandler(error_handler)
    
    # Log the configuration
    logger = logging.getLogger("logging_config")
    logger.info(f"🚀 Logging configured - Level: {logging.getLevelName(level)}, File: {log_to_file}")


def get_logger_config():
    """
    Get current logging configuration.
    
    Returns:
        dict: Current logging configuration
    """
    root_logger = logging.getLogger()
    
    config = {
        "level": root_logger.level,
        "level_name": logging.getLevelName(root_logger.level),
        "handlers": []
    }
    
    for handler in root_logger.handlers:
        handler_info = {
            "type": type(handler).__name__,
            "level": handler.level,
            "level_name": logging.getLevelName(handler.level)
        }
        
        if hasattr(handler, 'baseFilename'):
            handler_info["file"] = handler.baseFilename
        
        config["handlers"].append(handler_info)
    
    return config


def set_log_level(level: int):
    """
    Change the logging level for all handlers.
    
    Args:
        level: New logging level
    """
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # Update console handlers only (keep file handlers at DEBUG)
    for handler in root_logger.handlers:
        if isinstance(handler, logging.StreamHandler) and not isinstance(handler, logging.FileHandler):
            handler.setLevel(level)
    
    logger = logging.getLogger("logging_config")
    logger.info(f"🔧 Log level changed to {logging.getLevelName(level)}")


# Predefined configurations
DEVELOPMENT_CONFIG = {
    "level": logging.DEBUG,
    "log_to_file": True,
    "console_format": '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    "file_format": '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
}

PRODUCTION_CONFIG = {
    "level": logging.INFO,
    "log_to_file": True,
    "console_format": '%(asctime)s - %(levelname)s - %(message)s',
    "file_format": '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
}

TESTING_CONFIG = {
    "level": logging.WARNING,
    "log_to_file": False,
    "console_format": '%(levelname)s - %(message)s'
}


def configure_for_development():
    """Configure logging for development environment."""
    configure_logging(**DEVELOPMENT_CONFIG)


def configure_for_production():
    """Configure logging for production environment."""
    configure_logging(**PRODUCTION_CONFIG)


def configure_for_testing():
    """Configure logging for testing environment."""
    configure_logging(**TESTING_CONFIG)


if __name__ == "__main__":
    # Demo the logging configuration
    print("🔧 Logging Configuration Demo")
    print("=" * 40)
    
    # Configure for development
    configure_for_development()
    
    # Test logging
    logger = logging.getLogger("demo")
    logger.debug("🔍 Debug message")
    logger.info("ℹ️ Info message")
    logger.warning("⚠️ Warning message")
    logger.error("❌ Error message")
    
    # Show configuration
    config = get_logger_config()
    print(f"\n📊 Current configuration:")
    print(f"  Level: {config['level_name']}")
    print(f"  Handlers: {len(config['handlers'])}")
    for i, handler in enumerate(config['handlers']):
        print(f"    {i+1}. {handler['type']} - {handler['level_name']}")
        if 'file' in handler:
            print(f"       File: {handler['file']}")
    
    print(f"\n📁 Log files created in 'logs' directory")
    print(f"✅ Logging configuration demo complete")
