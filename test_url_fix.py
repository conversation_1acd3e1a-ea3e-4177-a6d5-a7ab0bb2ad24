#!/usr/bin/env python3
"""
Test to verify that our URL fix resolves the interpolation syntax issue.
"""

from urllib.parse import quote, quote_plus
from sqlalchemy import URL

def test_old_problematic_method():
    """Test the old method that caused interpolation issues."""
    print("🧪 Testing Old Problematic Method (quote_plus):")
    print("=" * 60)
    
    password = "Aa@123FgAPoO"  # Password with @ symbol
    username = "mycat"
    host = "mysql.3ren.inside.srt"
    port = 8066
    database = "easy_agent_center"
    charset = "utf8mb4"
    
    # This was the problematic approach
    password_encoded = quote_plus(password)
    url = f"mysql+pymysql://{username}:{password_encoded}@{host}:{port}/{database}?charset={charset}"
    
    print(f"Password: {password}")
    print(f"Encoded with quote_plus: {password_encoded}")
    print(f"URL: {url}")
    
    # Try to parse with SQLAlchemy
    try:
        from sqlalchemy import create_engine
        engine = create_engine(url)
        print("✅ SQLAlchemy accepted the URL")
        engine.dispose()
        return True
    except Exception as e:
        print(f"❌ SQLAlchemy rejected the URL: {e}")
        return False

def test_quote_method():
    """Test the quote method (intermediate fix)."""
    print("\n🧪 Testing Quote Method (safe=''):")
    print("=" * 60)
    
    password = "Aa@123FgAPoO"  # Password with @ symbol
    username = "mycat"
    host = "mysql.3ren.inside.srt"
    port = 8066
    database = "easy_agent_center"
    charset = "utf8mb4"
    
    # This was the intermediate approach
    password_encoded = quote(password, safe='')
    url = f"mysql+pymysql://{username}:{password_encoded}@{host}:{port}/{database}?charset={charset}"
    
    print(f"Password: {password}")
    print(f"Encoded with quote: {password_encoded}")
    print(f"URL: {url}")
    
    # Try to parse with SQLAlchemy
    try:
        from sqlalchemy import create_engine
        engine = create_engine(url)
        print("✅ SQLAlchemy accepted the URL")
        engine.dispose()
        return True
    except Exception as e:
        print(f"❌ SQLAlchemy rejected the URL: {e}")
        return False

def test_new_url_create_method():
    """Test the new SQLAlchemy URL.create method."""
    print("\n🧪 Testing New SQLAlchemy URL.create Method:")
    print("=" * 60)
    
    password = "Aa@123FgAPoO"  # Password with @ symbol
    username = "mycat"
    host = "mysql.3ren.inside.srt"
    port = 8066
    database = "easy_agent_center"
    charset = "utf8mb4"
    
    # This is our new approach
    try:
        url = URL.create(
            drivername="mysql+pymysql",
            username=username,
            password=password,
            host=host,
            port=port,
            database=database,
            query={"charset": charset}
        )
        
        print(f"Password: {password}")
        print(f"URL object: {url}")
        print(f"URL string: {str(url)}")
        
        # Try to parse with SQLAlchemy
        from sqlalchemy import create_engine
        engine = create_engine(url)
        print("✅ SQLAlchemy accepted the URL")
        engine.dispose()
        return True
        
    except Exception as e:
        print(f"❌ URL.create failed: {e}")
        return False

def test_edge_cases():
    """Test various edge cases with special characters."""
    print("\n🧪 Testing Edge Cases:")
    print("=" * 60)
    
    test_passwords = [
        "simple123",           # Simple password
        "pass@word",          # @ symbol
        "p@ss%w0rd",          # @ and % symbols
        "user:pass@host",     # Multiple special chars
        "密码@123",            # Unicode characters
        "p@ss w0rd",          # Space character
        "p@ss+w0rd",          # Plus character
    ]
    
    results = []
    
    for password in test_passwords:
        print(f"\nTesting password: '{password}'")
        
        try:
            url = URL.create(
                drivername="mysql+pymysql",
                username="testuser",
                password=password,
                host="localhost",
                port=3306,
                database="testdb",
                query={"charset": "utf8mb4"}
            )
            
            url_str = str(url)
            print(f"  ✅ URL created: {url_str}")
            results.append(True)
            
        except Exception as e:
            print(f"  ❌ Failed: {e}")
            results.append(False)
    
    success_rate = sum(results) / len(results) * 100
    print(f"\nSuccess rate: {success_rate:.1f}% ({sum(results)}/{len(results)})")
    
    return all(results)

if __name__ == "__main__":
    print("🔧 URL Fix Verification Test")
    print("=" * 70)
    
    results = []
    
    # Test all methods
    results.append(test_old_problematic_method())
    results.append(test_quote_method())
    results.append(test_new_url_create_method())
    results.append(test_edge_cases())
    
    print("\n" + "=" * 70)
    print("📊 Summary:")
    print(f"Old problematic method: {'✅' if results[0] else '❌'}")
    print(f"Quote method: {'✅' if results[1] else '❌'}")
    print(f"New URL.create method: {'✅' if results[2] else '❌'}")
    print(f"Edge cases: {'✅' if results[3] else '❌'}")
    
    if results[2]:  # New method works
        print("\n🎉 The URL fix is working correctly!")
        print("The interpolation syntax issue has been resolved.")
        print("The current connection issue is a database permission problem, not a URL encoding problem.")
    else:
        print("\n⚠️ The URL fix needs more work.")
