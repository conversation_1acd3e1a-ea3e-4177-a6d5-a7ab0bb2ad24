#!/usr/bin/env python3
"""
Test script for MCP periodic sync functionality.
"""

import asyncio
import sys
import os
import uuid

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.mcp.mcp_manager import MCPManager
from app.database.connection import get_database_manager
from app.database.models import MCPServer
from app.logger.logger import get_logger


async def test_mcp_sync():
    """Test MCP periodic sync functionality."""
    print("🧪 Testing MCP periodic sync functionality...")
    
    # Initialize database
    db_manager = get_database_manager()
    await db_manager.initialize()
    
    # Create test MCP manager with shorter sync interval
    mcp_manager = MCPManager(sync_interval=5)  # 5 second sync interval for testing
    await mcp_manager.initialize()  # Initialize asynchronously
    
    print("✅ MCP Manager initialized with 5-second sync interval")
    
    # Create a test MCP server directly in the database
    test_server_id = f"test-sync-server-{uuid.uuid4().hex[:8]}"
    
    async with db_manager.get_session() as session:
        test_server = MCPServer(
            server_id=test_server_id,
            name="Test Sync Server",
            command="echo",
            args=["hello"],
            description="Test server for sync functionality"
        )
        session.add(test_server)
        await session.commit()
        await session.refresh(test_server)
        
    print(f"✅ Created test MCP server: {test_server.name}")
    
    # Wait for sync to pick up the new server
    print("⏳ Waiting for sync to detect new server...")
    await asyncio.sleep(8)  # Wait for sync cycle
    
    # Check if server was loaded into memory
    if test_server_id in mcp_manager._servers:
        print("✅ Server successfully synced to memory")
    else:
        print("❌ Server not found in memory after sync")
        return False
    
    # Update server in database
    async with db_manager.get_session() as session:
        from sqlalchemy import update
        await session.execute(
            update(MCPServer)
            .where(MCPServer.server_id == test_server_id)
            .values(description="Updated description for sync test")
        )
        await session.commit()
        
    print("✅ Updated server description in database")
    
    # Wait for sync to pick up the change
    print("⏳ Waiting for sync to detect server update...")
    await asyncio.sleep(8)
    
    # Check if server config was updated in memory
    memory_server = mcp_manager._servers.get(test_server_id)
    if memory_server and memory_server.description == "Updated description for sync test":
        print("✅ Server configuration successfully updated in memory")
    else:
        print("❌ Server configuration not updated in memory")
        return False
    
    # Deactivate server in database
    async with db_manager.get_session() as session:
        await session.execute(
            update(MCPServer)
            .where(MCPServer.server_id == test_server_id)
            .values(is_active=False)
        )
        await session.commit()
        
    print("✅ Deactivated server in database")
    
    # Wait for sync to remove the server
    print("⏳ Waiting for sync to remove deactivated server...")
    await asyncio.sleep(8)
    
    # Check if server was removed from memory
    if test_server_id not in mcp_manager._servers:
        print("✅ Server successfully removed from memory")
    else:
        print("❌ Server not removed from memory after deactivation")
        return False
    
    # Shutdown MCP manager
    await mcp_manager.shutdown_all()
    print("✅ MCP Manager shut down")
    
    return True


async def main():
    """Main test function."""
    try:
        success = await test_mcp_sync()
        if success:
            print("\n🎉 All MCP sync tests passed!")
            sys.exit(0)
        else:
            print("\n❌ Some MCP sync tests failed!")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())