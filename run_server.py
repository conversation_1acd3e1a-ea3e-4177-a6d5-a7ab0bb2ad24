#!/usr/bin/env python3
"""
Easy Agent Center - Server Startup Script

This script starts the FastAPI server with proper configuration.
"""

import os
import sys
import uvicorn
from pathlib import Path

# Load configuration
try:
    from app.config.config import get_config
    config = get_config()
    if Path(".env").exists():
        print("✅ Loaded configuration from .env file")
except ImportError:
    # Fallback to direct environment variable loading
    try:
        from dotenv import load_dotenv

        # Look for .env file in current directory
        env_path = Path(".env")
        if env_path.exists():
            load_dotenv(env_path)
            print("✅ Loaded configuration from .env file")
    except ImportError:
        # python-dotenv not installed, skip loading .env file
        pass
    config = None

def main():
    """Start the FastAPI server."""
    print("🚀 Starting Easy Agent Center...")
    print("=" * 50)
    
    # Check if OpenAI API key is set
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠️  Warning: OPENAI_API_KEY environment variable not set")
        print("💡 Some agent features may not work without an API key")
        print("   Set it with: export OPENAI_API_KEY='your-key-here'")
        print()
    
    # Check if we're in the right directory
    if not Path("app/main.py").exists():
        print("❌ Error: app/main.py not found in current directory")
        print("💡 Make sure you're running this from the project root")
        sys.exit(1)
    
    print("🌐 Starting server at http://localhost:8000")
    print("📚 API documentation will be available at:")
    print("   - Swagger UI: http://localhost:8000/docs")
    print("   - ReDoc: http://localhost:8000/redoc")
    print()
    print("🛑 Press Ctrl+C to stop the server")
    print("=" * 50)
    
    try:
        # Get configuration
        if config:
            # Use configuration from config module
            server_config = config.server
            host = server_config.host
            port = server_config.port
            log_level = server_config.log_level
            reload = server_config.reload
        else:
            # Fallback to environment variables
            host = os.getenv("HOST", "0.0.0.0")
            port = int(os.getenv("PORT", "8000"))
            log_level = os.getenv("LOG_LEVEL", "info").lower()
            reload = os.getenv("ENVIRONMENT", "development") == "development"

        print(f"🌐 Starting server at http://{host}:{port}")
        if config:
            print(f"🔧 Environment: {config.environment}")
            if hasattr(config, 'database') and hasattr(config.database, 'host'):
                print(f"🗄️ Database: {config.database.host}:{config.database.port}/{config.database.database}")

        # Start the server in the background
        import subprocess
        
        command = [
            sys.executable,
            "-m",
            "uvicorn",
            "app.main:app",
            "--host", host,
            "--port", str(port),
            "--log-level", log_level,
        ]
        if reload:
            command.append("--reload")

        # Create directories for logs and PIDs if they don't exist
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        pid_dir = Path("pids")
        pid_dir.mkdir(exist_ok=True)

        # Redirect stdout and stderr to log files
        stdout_log_path = log_dir / "server_stdout.log"
        stderr_log_path = log_dir / "server_stderr.log"
        
        with open(stdout_log_path, "wb") as stdout_log, open(stderr_log_path, "wb") as stderr_log:
            process = subprocess.Popen(command, stdout=stdout_log, stderr=stderr_log, close_fds=True)

        # Save the PID to a file
        pid_file = pid_dir / "server.pid"
        with open(pid_file, "w") as f:
            f.write(str(process.pid))

        print("=" * 50)
        print(f"✅ Server started in the background with PID: {process.pid}")
        print(f"📄 PID saved to: {pid_file.resolve()}")
        print(f"📜 Logs are being written to:")
        print(f"   - STDOUT: {stdout_log_path.resolve()}")
        print(f"   - STDERR: {stderr_log_path.resolve()}")
        print(f"ℹ️ To stop the server, run: kill `cat {pid_file.resolve()}`")
        print("=" * 50)

    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
