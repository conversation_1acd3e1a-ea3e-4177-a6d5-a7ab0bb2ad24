# Easy Agent Center

A comprehensive agent system built on top of LlamaIndex, providing various types of agents for different use cases through a FastAPI web interface.

## Features

- 🤖 **Multiple Agent Types**: Support for ReAct and OpenAI function-calling agents
- 🛠️ **Tool Integration**: Easy integration of custom tools and functions
- 🌐 **REST API**: Complete FastAPI interface for agent management and interaction
- 💬 **Chat Interface**: Both synchronous and streaming chat capabilities
- 📊 **Agent Management**: Create, configure, and manage multiple agents
- 🔧 **Extensible**: Easy to add new agent types and tools
- 📝 **Comprehensive Logging**: Full logging system with error tracking and performance monitoring
- 🌍 **OpenAI-Compatible**: Works with any OpenAI-compatible service (Ollama, LocalAI, etc.)
- 🗄️ **MySQL Integration**: Optional MySQL database support for agent persistence and chat history
- 🔄 **Database Migrations**: Automatic schema migration system using Alembic

## Quick Start

### Prerequisites

- Python 3.12+
- OpenAI API key (for OpenAI agents) OR any OpenAI-compatible service
- MySQL 5.7+ (optional, for database features)
- Redis (optional, for caching)
- Node.js (optional, for MCP server support)

### Installation

1. **Clone the repository:**
```bash
git clone <repository-url>
cd easy-agent-center
```

2. **Install dependencies:**
```bash
# Using poetry (recommended):
poetry install

# Or using pip:
pip install -r requirements.txt
```

3. **Configure the application:**
```bash
# Copy the example environment file
cp .env.example .env

# Edit .env file with your configuration
# At minimum, set your LLM API keys
```

**Essential .env configuration:**
```bash
# LLM Configuration (Required)
OPENAI_API_KEY=your-openai-api-key-here
DEFAULT_API_BASE=https://api.openai.com/v1
DEFAULT_MODEL=gpt-3.5-turbo

# Database Configuration (Optional)
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your-mysql-password
DB_DATABASE=easy_agent_center

# Redis Configuration (Optional)
REDIS_URL=redis://localhost:6379/0

# Server Configuration
HOST=0.0.0.0
PORT=8000
LOG_LEVEL=INFO
ENVIRONMENT=development
```

4. **Initialize the database (if using MySQL):**
```bash
# Auto-initialize database and run migrations
python manage_db.py auto-upgrade
```

### Quick Demo

Run the interactive demo to see the system in action:
```bash
# Basic agent demo
python demo/demo_agent.py

# RAG demo (if available)
python demo/demo_rag.py
```

### Running the Application

**Start the server:**
```bash
# Using the provided script (recommended):
python run_server.py

# Or directly with uvicorn:
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

**Server will be available at:**
- 🌐 **Main API**: http://localhost:8000
- 📚 **Swagger UI**: http://localhost:8000/docs  
- 📖 **ReDoc**: http://localhost:8000/redoc

**Stop the server:**
```bash
# If started with run_server.py:
python stop_server.py

# Or find and kill the process:
kill `cat pids/server.pid`
```

## API Documentation

Once the server is running, you can access:
- **Interactive API docs**: http://localhost:8000/docs
- **ReDoc documentation**: http://localhost:8000/redoc

## Usage Examples

### Basic Chat

```bash
curl -X POST "http://localhost:8000/chat" \
     -H "Content-Type: application/json" \
     -d '{"message": "Hello! What can you help me with?"}'
```

### Create a New Agent

```bash
curl -X POST "http://localhost:8000/agents" \
     -H "Content-Type: application/json" \
     -d '{
       "agent_id": "my_agent",
       "agent_type": "react",
       "name": "My Custom Agent",
       "description": "A custom agent for specific tasks",
       "system_prompt": "You are a helpful assistant specialized in..."
     }'
```

### List All Agents

```bash
curl -X GET "http://localhost:8000/agents"
```

### Chat with Specific Agent

```bash
curl -X POST "http://localhost:8000/chat" \
     -H "Content-Type: application/json" \
     -d '{
       "message": "Calculate 25 * 17",
       "agent_id": "my_agent"
     }'
```

## Python Usage

### Basic Agent Creation

```python
from agents.agent_manager import AgentManager
from llama_index.llms.openai import OpenAI

# Create an agent manager
manager = AgentManager()

# Create custom LLM (for OpenAI-compatible services)
llm = OpenAI(
    api_base="http://localhost:11434/v1",  # Ollama example
    api_key="ollama",
    model="llama2"
)

# Create an agent using LlamaIndex built-in workflow
agent = manager.create_agent(
    agent_id="my_assistant",
    agent_type="react",  # or "openai" for FunctionAgent
    name="My Assistant",
    description="A helpful AI assistant",
    llm=llm,  # Required: must provide LLM
    system_prompt="You are a helpful assistant.",
    set_as_default=True
)

# Chat with the agent
response = await manager.chat_with_agent("Hello!")
print(response)
```

### Adding Tools

```python
from agents.tools import AVAILABLE_TOOLS

# Add global tools (available to all agents)
for tool in AVAILABLE_TOOLS:
    manager.add_global_tool(tool)

# Create an agent with tools
agent = manager.create_agent(
    agent_id="tool_agent",
    agent_type="react",  # ReAct agents work well with tools
    name="Tool Agent",
    description="An agent with access to various tools"
)

# The agent can now use weather, calculator, web search, and time tools
response = await manager.chat_with_agent("What's the weather in Tokyo?")
```

### Multiple Agents

```python
# Create specialized agents
math_agent = manager.create_agent(
    agent_id="math_expert",
    agent_type="openai",
    name="Math Expert",
    system_prompt="You are a mathematics expert."
)

weather_agent = manager.create_agent(
    agent_id="weather_expert",
    agent_type="react",
    name="Weather Expert",
    system_prompt="You are a weather information specialist."
)

# Chat with specific agents
math_response = await manager.chat_with_agent(
    "Calculate the area of a circle with radius 5",
    agent_id="math_expert"
)

weather_response = await manager.chat_with_agent(
    "What's the weather in London?",
    agent_id="weather_expert"
)
```

## OpenAI-Compatible Services

Easy Agent Center supports any OpenAI-compatible service! You can use:

### 🦙 Local Services
- **Ollama**: Run models locally with `ollama serve`
- **LocalAI**: Self-hosted OpenAI alternative
- **vLLM**: High-performance inference server

### ☁️ Cloud Services
- **OpenAI**: Official OpenAI API
- **Azure OpenAI**: Microsoft's OpenAI service
- **Hugging Face Inference Endpoints**: Hosted model endpoints
- **Any custom OpenAI-compatible API**

### Configuration Example

```python
# Using Ollama (local)
agent = manager.create_agent(
    agent_id="local_agent",
    agent_type="openai",
    name="Local Ollama Agent",
    api_base="http://localhost:11434/v1",
    api_key="ollama",  # Any string works
    model="llama2"
)

# Using custom service
agent = manager.create_agent(
    agent_id="custom_agent",
    agent_type="openai",
    name="Custom Service Agent",
    api_base="https://your-service.com/v1",
    api_key="your-api-key",
    model="your-model"
)
```

### API Example

```bash
curl -X POST "http://localhost:8000/agents" \
     -H "Content-Type: application/json" \
     -d '{
       "agent_id": "ollama_agent",
       "agent_type": "openai",
       "name": "Ollama Agent",
       "api_base": "http://localhost:11434/v1",
       "api_key": "ollama",
       "model": "llama2"
     }'
```

## Agent Types

Easy Agent Center now uses **LlamaIndex's built-in workflow agents** for better reliability and performance.

### ReAct Agent (LlamaIndex Built-in)
- Uses LlamaIndex's native ReActAgent workflow
- Implements the ReAct (Reasoning and Acting) paradigm
- Better at multi-step reasoning and tool usage
- Works with any OpenAI-compatible service
- Best for: Complex problem solving, tool-heavy workflows

### Function Agent (LlamaIndex Built-in)
- Uses LlamaIndex's native FunctionAgent workflow
- Optimized for function calling capabilities
- More efficient and reliable tool usage
- Works with any OpenAI-compatible service
- Best for: General conversation, function calling

### Key Benefits of Built-in Agents
- ✅ **Better maintained**: Actively developed by LlamaIndex team
- ✅ **More robust**: Thoroughly tested and optimized
- ✅ **Latest features**: Access to newest LlamaIndex capabilities
- ✅ **Stateless workflows**: Clean execution model
- ✅ **Comprehensive logging**: Full error tracking and debugging

## Logging System

Easy Agent Center includes a comprehensive logging system that tracks all operations, errors, and performance metrics.

### Features

- **📝 Multiple Log Levels**: DEBUG, INFO, WARNING, ERROR, CRITICAL
- **📁 File Logging**: Automatic log file creation with daily rotation
- **🖥️ Console Output**: Real-time logging to console
- **📊 Performance Tracking**: Automatic timing of operations
- **❌ Error Tracking**: Full stack traces for debugging
- **🤖 Agent Operations**: Detailed logging of agent activities

### Log Files

Logs are automatically created in the `logs/` directory:

- `agents_YYYYMMDD.log` - Daily agent operations log
- `agents_full_YYYYMMDD.log` - Complete system log with DEBUG level
- `errors_YYYYMMDD.log` - Error-only log file

### Usage Examples

```python
from agents import get_logger, setup_logging

# Initialize logging
setup_logging()
logger = get_logger("my_component")

# Basic logging
logger.info("Operation started")
logger.warning("Potential issue detected")

# Error logging with stack trace
try:
    # Some operation
    pass
except Exception as e:
    logger.error("Operation failed", exception=e)

# Performance logging
from agents import PerformanceLogger
with PerformanceLogger("slow_operation", logger):
    # Your code here
    pass
```

### Configuration

```python
from agents.logger import setup_logging
import logging

# Configure logging level
setup_logging(level=logging.DEBUG)

# Or use predefined configurations
from logging_config import configure_for_development
configure_for_development()
```

## Available Tools

The system comes with several built-in tools:

- **Weather Tool**: Get weather information for cities
- **Calculator Tool**: Perform mathematical calculations
- **Web Search Tool**: Search the web for information
- **Time Tool**: Get current date and time

## MCP (Model Context Protocol) Integration

Easy Agent Center supports MCP servers, allowing agents to use tools and resources from MCP-compatible services.

### What is MCP?

The Model Context Protocol (MCP) is an open standard that enables AI applications to securely connect to external data sources and tools. MCP servers provide capabilities like:

- **File System Operations**: Read, write, and manage files
- **Database Access**: Query and manipulate databases (SQLite, PostgreSQL, etc.)
- **Web Search**: Search the web using various search engines
- **API Integrations**: Connect to GitHub, Slack, and other services
- **Memory Storage**: Persistent memory for conversations
- **Time Utilities**: Date and time operations

### Using MCP Servers

#### 1. Install Node.js (Required)
Most MCP servers require Node.js. Install from [nodejs.org](https://nodejs.org/)

#### 2. Create MCP Server via API

```bash
curl -X POST "http://localhost:8000/mcp/servers" \
  -H "Content-Type: application/json" \
  -d '{
    "server_id": "filesystem",
    "name": "File System Server",
    "description": "Provides file system operations",
    "command": "npx",
    "args": ["-y", "@modelcontextprotocol/server-filesystem", "/tmp"],
    "is_active": true,
    "auto_start": true
  }'
```

#### 3. Available Example Servers

- **filesystem**: File system operations
- **sqlite**: SQLite database operations
- **memory**: Persistent memory storage
- **time**: Time and date utilities
- **brave_search**: Web search (requires API key)
- **github**: GitHub operations (requires token)
- **postgres**: PostgreSQL operations (requires database)

#### 4. List MCP Servers

```bash
curl "http://localhost:8000/mcp/servers"
```

#### 5. Get MCP Tools

```bash
curl "http://localhost:8000/mcp/tools"
```

### MCP Server Management

#### Start/Stop Servers

```bash
# Start a server
curl -X POST "http://localhost:8000/mcp/servers/filesystem/start"

# Stop a server
curl -X POST "http://localhost:8000/mcp/servers/filesystem/stop"

# Restart a server
curl -X POST "http://localhost:8000/mcp/servers/filesystem/restart"
```

#### Server Status

```bash
curl "http://localhost:8000/mcp/servers/filesystem/status"
```

### Agent Integration

Once MCP servers are running, their tools are automatically available to all agents. Agents can use MCP tools alongside built-in tools seamlessly.

```python
# MCP tools are automatically included when creating agents
agent = manager.create_agent(
    agent_id="mcp_agent",
    name="MCP-Enabled Agent",
    description="An agent with access to MCP tools",
    system_prompt="You can use file system, database, and web search tools."
)

# Chat with agent - it can now use MCP tools
response = await manager.chat_with_agent("List files in /tmp directory")
```

### Custom MCP Servers

You can create custom MCP server configurations:

```python
from app.mcp.examples import create_custom_server_config

config = create_custom_server_config(
    server_id="my_custom_server",
    name="My Custom Server",
    command="python",
    args=["my_mcp_server.py"],
    description="My custom MCP server"
)
```

## Custom Tools

You can easily add custom tools:

```python
from llama_index.core.tools import FunctionTool

def my_custom_function(param: str) -> str:
    """My custom function description."""
    return f"Processed: {param}"

# Create a tool from the function
custom_tool = FunctionTool.from_defaults(fn=my_custom_function)

# Add to agent manager
manager.add_global_tool(custom_tool)
```

## Running Examples

### Basic Usage Examples
```bash
python examples/basic_usage.py
```

### LlamaIndex Built-in Agents Examples
```bash
# Demonstrate LlamaIndex workflow agents
python examples/llamaindex_agents_example.py
```

### OpenAI-Compatible Services Examples
```bash
# Python examples for different services
python examples/openai_compatible_services.py

# API examples for different services
python examples/api_openai_compatible.py
```

### Logging Examples
```bash
# Demonstrate logging functionality
python examples/logging_example.py

# Configure logging for different environments
python logging_config.py
```

### API Examples
```bash
# Make sure the server is running first
uvicorn main:app --reload

# In another terminal:
python examples/api_examples.py
```

## Testing

Run the test suite:
```bash
pytest tests/ -v
```

## Project Structure

```
easy-agent-center/
├── agents/                 # Core agents module
│   ├── __init__.py
│   ├── base_agent.py      # Abstract base agent class
│   ├── react_agent.py     # ReAct agent implementation
│   ├── openai_agent.py    # OpenAI agent implementation
│   ├── agent_manager.py   # Agent management system
│   └── tools.py           # Built-in tools
├── examples/              # Usage examples
│   ├── basic_usage.py     # Python usage examples
│   └── api_examples.py    # API usage examples
├── tests/                 # Test suite
│   └── test_agents.py     # Agent tests
├── main.py               # FastAPI application
├── pyproject.toml        # Project configuration
└── README.md            # This file
```

## Configuration

The application uses environment variables for configuration. You can set these variables directly or use a `.env` file for convenience.

### Using .env File (Recommended)

1. Copy the example file:
```bash
cp .env.example .env
```

2. Edit the `.env` file with your configuration:
```bash
# LLM Configuration
OPENAI_API_KEY=your-openai-api-key-here
DEFAULT_API_BASE=https://api.openai.com/v1
DEFAULT_MODEL=gpt-3.5-turbo

# MySQL Database (optional)
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your-password
DB_DATABASE=easy_agent_center

# Server Configuration
HOST=0.0.0.0
PORT=8000
LOG_LEVEL=INFO
ENVIRONMENT=development
```

### Environment Variables Reference

#### Core Configuration
- `OPENAI_API_KEY`: Your OpenAI API key (required for OpenAI agents)
- `DEFAULT_API_BASE`: Default API base URL for LLM services
- `DEFAULT_API_KEY`: Default API key for LLM services
- `DEFAULT_MODEL`: Default model name (default: gpt-3.5-turbo)

#### Server Configuration
- `HOST`: Server host (default: 0.0.0.0)
- `PORT`: Server port (default: 8000)
- `LOG_LEVEL`: Logging level (default: INFO)
- `ENVIRONMENT`: Environment mode (development/production/testing)

#### MySQL Database Configuration (Optional)
- `DB_HOST`: Database host (default: localhost)
- `DB_PORT`: Database port (default: 3306)
- `DB_USERNAME`: Database username (default: root)
- `DB_PASSWORD`: Database password (default: empty)
- `DB_DATABASE`: Database name (default: easy_agent_center)
- `DB_CHARSET`: Database charset (default: utf8mb4)
- `DB_POOL_SIZE`: Connection pool size (default: 10)
- `DB_MAX_OVERFLOW`: Max overflow connections (default: 20)
- `DB_POOL_TIMEOUT`: Pool timeout in seconds (default: 30)
- `DB_POOL_RECYCLE`: Pool recycle time in seconds (default: 3600)
- `DB_ECHO`: Enable SQL echo for debugging (default: false)

#### Logging Configuration
- `LOG_TO_FILE`: Enable file logging (default: true)
- `LOG_DIR`: Log directory (default: logs)

### Agent Configuration

Agents can be configured with:
- `system_prompt`: Custom system prompt
- `max_iterations`: Maximum reasoning iterations (ReAct agents)
- `verbose`: Enable verbose output
- `temperature`: LLM temperature setting

## Database Migrations

The application includes an automatic database migration system using Alembic to handle schema changes safely.

### Automatic Migration on Startup

When the application starts, it automatically:
1. Creates the database if it doesn't exist
2. Checks for pending migrations
3. Applies any pending migrations automatically
4. Falls back to direct table creation if migrations fail

### Manual Migration Management

Use the `manage_db.py` script for manual migration management:

```bash
# Check migration status
python manage_db.py status --history

# Create a new migration (auto-detects model changes)
python manage_db.py create "Add new field to user table"

# Apply pending migrations
python manage_db.py upgrade

# Rollback to a specific revision
python manage_db.py downgrade <revision_id>

# Initialize database with migrations
python manage_db.py init

# Auto-upgrade if needed
python manage_db.py auto-upgrade
```

### Schema Change Workflow

When you modify database models:

1. **Modify the model** in `database/models.py`:
```python
class ChatMessage(Base):
    # ... existing fields ...
    priority = Column(Integer, default=0, nullable=False)  # NEW FIELD
```

2. **Create migration**:
```bash
python manage_db.py create "Add priority field to chat_messages"
```

3. **Review generated migration** in `alembic/versions/`:
```python
def upgrade() -> None:
    op.add_column('chat_messages', sa.Column('priority', sa.Integer(), nullable=False, server_default='0'))

def downgrade() -> None:
    op.drop_column('chat_messages', 'priority')
```

4. **Apply migration**:
```bash
python manage_db.py upgrade
```

### Migration Safety

- ✅ **Automatic backups**: Always backup your database before applying migrations
- ✅ **Rollback support**: All migrations include rollback instructions
- ✅ **Version control**: Migration files are version controlled with your code
- ✅ **Team collaboration**: Migrations ensure consistent schema across environments
- ✅ **Production safety**: Test migrations in development before production deployment

### Migration Examples

See `examples/migration_example.py` for detailed examples of:
- Creating and applying migrations
- Handling schema changes
- Rolling back migrations
- Migration status checking

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For questions and support, please open an issue on the GitHub repository.
