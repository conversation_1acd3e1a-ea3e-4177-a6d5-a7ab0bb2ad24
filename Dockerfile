# 使用Python 3.12作为基础镜像
FROM python:3.12-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    PIP_INDEX_URL=https://pypi.tuna.tsinghua.edu.cn/simple/ \
    PIP_TRUSTED_HOST=pypi.tuna.tsinghua.edu.cn

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    telnet \
    net-tools \
    ntpdate \
    wget \
    vim \
    iputils-ping \
    procps \
    && rm -rf /var/lib/apt/lists/*

# 安装NodeSource Node.js v22 repository
RUN curl -fsSL https://deb.nodesource.com/setup_22.x | bash -

# 安装Node.js v22
RUN apt-get update && apt-get install -y \
    nodejs \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY pyproject.toml poetry.lock ./

# 安装Poetry 2.1.3 (使用清华镜像源)
RUN pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ poetry==2.1.3

# 配置Poetry
RUN poetry config virtualenvs.create false

# 安装项目依赖
RUN poetry install --only main --no-interaction --no-ansi -vvv

# 复制应用代码
COPY . .

# 复制启动脚本并设置执行权限
COPY startup.sh /app/startup.sh
RUN chmod +x /app/startup.sh

# 创建日志和PID目录
RUN mkdir -p logs pids

# 暴露端口
EXPOSE 8000

# 创建非root用户
RUN useradd --create-home --shell /bin/bash appuser
RUN chown -R appuser:appuser /app
# 确保Node.js和npm对appuser可用
USER appuser

# 启动应用
CMD ["/app/startup.sh"]