# Knowledge Base Module

This module provides a comprehensive knowledge base system for the Easy Agent Center, allowing users to create different knowledge bases, upload documents, and perform vector search operations.

## Features

- **Knowledge Base Management**: Create, update, and delete knowledge bases
- **Document Upload**: Upload various document types (text, PDF, Word, etc.)
- **Vector Storage**: Automatic vectorization and storage in ChromaDB
- **Semantic Search**: Search documents using natural language queries
- **Access Control**: Public/private knowledge bases with user-based access
- **Processing Status**: Track document processing status
- **Statistics**: Get knowledge base usage statistics

## API Endpoints

### Knowledge Base Management

- `POST /api/knowledge/bases` - Create a new knowledge base
- `GET /api/knowledge/bases` - List knowledge bases
- `GET /api/knowledge/bases/{kb_id}` - Get knowledge base details
- `PUT /api/knowledge/bases/{kb_id}` - Update knowledge base
- `DELETE /api/knowledge/bases/{kb_id}` - Delete knowledge base

### Document Management

- `POST /api/knowledge/bases/{kb_id}/documents` - Upload document
- `GET /api/knowledge/bases/{kb_id}/documents` - List documents
- `GET /api/knowledge/documents/{doc_id}` - Get document details
- `DELETE /api/knowledge/documents/{doc_id}` - Delete document

### Search & Analytics

- `POST /api/knowledge/bases/{kb_id}/search` - Search documents
- `GET /api/knowledge/bases/{kb_id}/stats` - Get knowledge base statistics
- `POST /api/knowledge/documents/{doc_id}/reprocess` - Reprocess document

## Usage Examples

### 1. Create a Knowledge Base

```bash
curl -X POST http://localhost:8000/api/knowledge/bases \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My Knowledge Base",
    "description": "A collection of technical documents",
    "is_public": false
  }'
```

### 2. Upload a Document

```bash
curl -X POST http://localhost:8000/api/knowledge/bases/{kb_id}/documents \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@document.txt" \
  -F "metadata={\"source\": \"manual\", \"type\": \"technical\"}"
```

### 3. Search Documents

```bash
curl -X POST http://localhost:8000/api/knowledge/bases/{kb_id}/search \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "machine learning algorithms",
    "limit": 10
  }'
```

### 4. Get Statistics

```bash
curl -X GET http://localhost:8000/api/knowledge/bases/{kb_id}/stats \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Supported File Types

- Text files (.txt)
- Markdown files (.md)
- PDF files (.pdf)
- Word documents (.docx)
- PowerPoint presentations (.pptx)
- Excel spreadsheets (.xlsx)

## Database Schema

### KnowledgeBase
- `kb_id`: Unique identifier
- `name`: Knowledge base name
- `description`: Optional description
- `created_by`: User who created it
- `is_public`: Public/private flag
- `kb_config`: JSON configuration

### Document
- `doc_id`: Unique identifier
- `kb_id`: Associated knowledge base
- `filename`: Original filename
- `file_path`: Storage path
- `file_size`: File size in bytes
- `file_type`: MIME type
- `content_hash`: SHA256 hash
- `status`: Processing status (pending/processing/completed/failed)
- `doc_metadata`: JSON metadata

### DocumentChunk
- `chunk_id`: Unique identifier
- `doc_id`: Associated document
- `kb_id`: Associated knowledge base
- `chunk_index`: Position in document
- `content`: Chunk text content
- `token_count`: Number of tokens
- `vector_id`: ChromaDB vector ID
- `chunk_metadata`: JSON metadata

## Configuration

The module uses the following configuration:

- **ChromaDB**: Stores vectors in `chroma_db/` directory
- **Uploads**: Stores files in `uploads/{kb_id}/` directories
- **Embedding**: Uses OpenAI-like embeddings (configurable)
- **Chunking**: 1000 tokens per chunk with 200 token overlap

## Testing

Run the test script:

```bash
python test_knowledge_base.py
```

## Migration

After deployment, run the database migration:

```bash
alembic upgrade head
```