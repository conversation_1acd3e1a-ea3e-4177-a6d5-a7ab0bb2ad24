"""add users table

Revision ID: 353bce99f5d6
Revises: ac90bcacc051
Create Date: 2025-07-22 17:40:20.542477

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '353bce99f5d6'
down_revision = 'ac90bcacc051'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('users',
    sa.<PERSON>umn('id', sa.Integer(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('username', sa.String(length=255), nullable=False, comment='用户名'),
    sa.Column('hashed_password', sa.String(length=255), nullable=False, comment='哈希密码'),
    sa.Column('is_active', sa.<PERSON>(), nullable=False, comment='是否激活'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    comment='用户表'
    )
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_table('users')
    # ### end Alembic commands ###
