"""Chat_history_add_user_name

Revision ID: 0a56495ecbc7
Revises: 9c779d324d9e
Create Date: 2025-07-17 09:54:13.863540

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0a56495ecbc7'
down_revision = '9c779d324d9e'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_histories', sa.Column('user_name', sa.String(length=255), nullable=False, comment='用户标识'))
    op.create_index(op.f('ix_chat_histories_user_name'), 'chat_histories', ['user_name'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_chat_histories_user_name'), table_name='chat_histories')
    op.drop_column('chat_histories', 'user_name')
    # ### end Alembic commands ###
