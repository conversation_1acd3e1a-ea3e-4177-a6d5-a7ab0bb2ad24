"""add_agent_config

Revision ID: a40aae1a46e5
Revises: 76bceef10ee1
Create Date: 2025-08-04 14:18:05.332428

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a40aae1a46e5'
down_revision = '76bceef10ee1'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('agent_configs', sa.Column('chat_recommend', sa.JSON(), nullable=True, comment='对话推荐'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('agent_configs', 'chat_recommend')
    # ### end Alembic commands ###
