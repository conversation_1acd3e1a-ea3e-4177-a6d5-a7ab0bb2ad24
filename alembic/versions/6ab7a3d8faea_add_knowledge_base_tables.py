"""Add knowledge base tables

Revision ID: 6ab7a3d8faea
Revises: 353bce99f5d6
Create Date: 2025-07-25 16:54:27.255014

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6ab7a3d8faea'
down_revision = '353bce99f5d6'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('document_chunks',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('chunk_id', sa.String(length=255), nullable=False, comment='分块的唯一标识符'),
    sa.Column('doc_id', sa.String(length=255), nullable=False, comment='所属文档 ID'),
    sa.Column('kb_id', sa.String(length=255), nullable=False, comment='所属知识库 ID'),
    sa.Column('chunk_index', sa.Integer(), nullable=False, comment='分块索引'),
    sa.Column('content', sa.Text(), nullable=False, comment='分块内容'),
    sa.Column('token_count', sa.Integer(), nullable=False, comment='分块令牌数'),
    sa.Column('vector_id', sa.String(length=255), nullable=True, comment='向量存储 ID'),
    sa.Column('chunk_metadata', sa.JSON(), nullable=True, comment='分块元数据 (JSON)'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.PrimaryKeyConstraint('id'),
    comment='文档分块表'
    )
    op.create_index(op.f('ix_document_chunks_chunk_id'), 'document_chunks', ['chunk_id'], unique=True)
    op.create_index(op.f('ix_document_chunks_doc_id'), 'document_chunks', ['doc_id'], unique=False)
    op.create_index(op.f('ix_document_chunks_kb_id'), 'document_chunks', ['kb_id'], unique=False)
    op.create_table('documents',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('doc_id', sa.String(length=255), nullable=False, comment='文档的唯一标识符'),
    sa.Column('kb_id', sa.String(length=255), nullable=False, comment='所属知识库 ID'),
    sa.Column('filename', sa.String(length=500), nullable=False, comment='原始文件名'),
    sa.Column('file_path', sa.String(length=1000), nullable=False, comment='文件存储路径'),
    sa.Column('file_size', sa.Integer(), nullable=False, comment='文件大小 (字节)'),
    sa.Column('file_type', sa.String(length=100), nullable=False, comment='文件类型'),
    sa.Column('content_hash', sa.String(length=64), nullable=False, comment='内容哈希值'),
    sa.Column('status', sa.String(length=50), nullable=False, comment='处理状态'),
    sa.Column('processing_error', sa.Text(), nullable=True, comment='处理错误信息'),
    sa.Column('chunk_count', sa.Integer(), nullable=False, comment='分块数量'),
    sa.Column('doc_metadata', sa.JSON(), nullable=True, comment='文档元数据 (JSON)'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    comment='文档表'
    )
    op.create_index(op.f('ix_documents_doc_id'), 'documents', ['doc_id'], unique=True)
    op.create_index(op.f('ix_documents_kb_id'), 'documents', ['kb_id'], unique=False)
    op.create_table('knowledge_bases',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('kb_id', sa.String(length=255), nullable=False, comment='知识库的唯一标识符'),
    sa.Column('name', sa.String(length=255), nullable=False, comment='知识库名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='知识库描述'),
    sa.Column('created_by', sa.String(length=255), nullable=False, comment='创建者'),
    sa.Column('is_public', sa.Boolean(), nullable=False, comment='是否为公共知识库'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否激活'),
    sa.Column('kb_config', sa.JSON(), nullable=True, comment='知识库配置 (JSON)'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    comment='知识库表'
    )
    op.create_index(op.f('ix_knowledge_bases_kb_id'), 'knowledge_bases', ['kb_id'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_knowledge_bases_kb_id'), table_name='knowledge_bases')
    op.drop_table('knowledge_bases')
    op.drop_index(op.f('ix_documents_kb_id'), table_name='documents')
    op.drop_index(op.f('ix_documents_doc_id'), table_name='documents')
    op.drop_table('documents')
    op.drop_index(op.f('ix_document_chunks_kb_id'), table_name='document_chunks')
    op.drop_index(op.f('ix_document_chunks_doc_id'), table_name='document_chunks')
    op.drop_index(op.f('ix_document_chunks_chunk_id'), table_name='document_chunks')
    op.drop_table('document_chunks')
    # ### end Alembic commands ###
