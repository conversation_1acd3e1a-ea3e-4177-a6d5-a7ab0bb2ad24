"""add_chat_invalid

Revision ID: 76bceef10ee1
Revises: d526cfa251c4
Create Date: 2025-08-01 16:13:27.684800

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '76bceef10ee1'
down_revision = 'd526cfa251c4'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_histories', sa.Column('invalid', sa.Integer(), nullable=False, comment='删除标记：0 有效，1 已删除'))
    op.add_column('chat_messages', sa.Column('invalid', sa.Integer(), nullable=False, comment='删除标记：0 有效，1 已删除'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('chat_messages', 'invalid')
    op.drop_column('chat_histories', 'invalid')
    # ### end Alembic commands ###
