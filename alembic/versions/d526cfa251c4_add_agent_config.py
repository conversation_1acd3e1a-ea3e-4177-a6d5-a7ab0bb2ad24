"""add_agent_config

Revision ID: d526cfa251c4
Revises: 6ab7a3d8faea
Create Date: 2025-08-01 15:50:00.426883

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd526cfa251c4'
down_revision = '6ab7a3d8faea'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('agent_configs',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('agent_id', sa.String(length=255), nullable=False, comment='关联的 Agent ID'),
    sa.Column('icon', sa.String(length=500), nullable=True, comment='Agent 图标 URL 或标识'),
    sa.Column('is_public', sa.Enum('public', 'private', 'partial', name='agent_visibility'), nullable=False, comment='公开范围：public 全公开，private 不公开，partial 半公开'),
    sa.Column('user_scope', sa.JSON(), nullable=True, comment='半公开时可见的用户ID列表 (JSON 数组)'),
    sa.Column('is_pinned', sa.Boolean(), nullable=False, comment='是否置顶'),
    sa.Column('pin_order', sa.Integer(), nullable=True, comment='置顶排序，值越小越靠前'),
    sa.Column('extra', sa.JSON(), nullable=True, comment='其他业务属性'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    comment='Agent 配置扩展表'
    )
    op.create_index(op.f('ix_agent_configs_agent_id'), 'agent_configs', ['agent_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_agent_configs_agent_id'), table_name='agent_configs')
    op.drop_table('agent_configs')
    # ### end Alembic commands ###
