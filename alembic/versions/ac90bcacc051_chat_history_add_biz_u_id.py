"""chat_history add biz_u_id

Revision ID: ac90bcacc051
Revises: 0a56495ecbc7
Create Date: 2025-07-18 16:13:30.401817

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ac90bcacc051'
down_revision = '0a56495ecbc7'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_histories', sa.Column('biz_u_id', sa.String(length=255), nullable=True, comment='业务用户id'))
    op.create_index(op.f('ix_chat_histories_biz_u_id'), 'chat_histories', ['biz_u_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_chat_histories_biz_u_id'), table_name='chat_histories')
    op.drop_column('chat_histories', 'biz_u_id')
    # ### end Alembic commands ###
