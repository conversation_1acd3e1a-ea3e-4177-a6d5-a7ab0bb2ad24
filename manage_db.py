#!/usr/bin/env python3
"""
Database management CLI for Easy Agent Center.

This script provides commands for managing database migrations.
"""

import argparse
import asyncio
import sys
from pathlib import Path

# Load environment variables
try:
    from dotenv import load_dotenv
    
    env_path = Path(".env")
    if env_path.exists():
        load_dotenv(env_path)
except ImportError:
    pass

from app.database.migrations import get_migration_manager
from app.database.connection import get_database_manager
from app.logger.logger import get_logger

logger = get_logger("manage_db")


def create_migration(args):
    """Create a new migration."""
    migration_manager = get_migration_manager()
    
    try:
        revision = migration_manager.create_migration(
            message=args.message,
            autogenerate=args.autogenerate
        )
        print(f"✅ Created migration: {revision}")
        print(f"📝 Message: {args.message}")
        
        if args.autogenerate:
            print("🔍 Auto-generated from model changes")
        else:
            print("📄 Empty migration created")
            
    except Exception as e:
        print(f"❌ Failed to create migration: {e}")
        sys.exit(1)


def upgrade_database(args):
    """Upgrade database to target revision."""
    migration_manager = get_migration_manager()
    
    try:
        revision = args.revision or "head"
        migration_manager.upgrade(revision)
        print(f"✅ Database upgraded to: {revision}")
        
    except Exception as e:
        print(f"❌ Failed to upgrade database: {e}")
        sys.exit(1)


def downgrade_database(args):
    """Downgrade database to target revision."""
    migration_manager = get_migration_manager()
    
    try:
        migration_manager.downgrade(args.revision)
        print(f"✅ Database downgraded to: {args.revision}")
        
    except Exception as e:
        print(f"❌ Failed to downgrade database: {e}")
        sys.exit(1)


def show_status(args):
    """Show migration status."""
    migration_manager = get_migration_manager()
    
    try:
        status = migration_manager.check_migration_status()
        
        print("📊 Database Migration Status")
        print("=" * 30)
        print(f"Status: {status['status']}")
        print(f"Message: {status['message']}")
        print(f"Current Revision: {status.get('current_revision', 'None')}")
        
        if 'latest_revision' in status:
            print(f"Latest Revision: {status['latest_revision']}")
        
        if 'pending_migrations' in status and isinstance(status['pending_migrations'], int):
            print(f"Pending Migrations: {status['pending_migrations']}")
        
        # Show migration history if requested
        if args.history:
            print("\n📜 Migration History")
            print("-" * 20)
            history = migration_manager.get_migration_history()
            
            if not history:
                print("No migrations found")
            else:
                for i, rev in enumerate(history):
                    marker = "→" if rev['revision'] == status.get('current_revision') else " "
                    print(f"{marker} {rev['revision'][:8]} - {rev['message']}")
        
    except Exception as e:
        print(f"❌ Failed to get migration status: {e}")
        sys.exit(1)


async def init_database(args):
    """Initialize database with current schema."""
    try:
        db_manager = get_database_manager()
        await db_manager.initialize(use_migrations=args.use_migrations)
        print("✅ Database initialized successfully")
        
    except Exception as e:
        print(f"❌ Failed to initialize database: {e}")
        sys.exit(1)


def auto_upgrade(args):
    """Auto-upgrade database if needed."""
    migration_manager = get_migration_manager()
    
    try:
        status = migration_manager.check_migration_status()
        print(f"Current status: {status['message']}")
        
        if status['status'] in ['up_to_date', 'no_migrations']:
            print("✅ Database is already up to date")
            return
        
        print("🔄 Running auto-upgrade...")
        success = migration_manager.auto_upgrade()
        
        if success:
            print("✅ Auto-upgrade completed successfully")
        else:
            print("❌ Auto-upgrade failed")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Auto-upgrade failed: {e}")
        sys.exit(1)


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Database management CLI for Easy Agent Center"
    )
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Create migration command
    create_parser = subparsers.add_parser("create", help="Create a new migration")
    create_parser.add_argument("message", help="Migration message/description")
    create_parser.add_argument(
        "--no-autogenerate", 
        dest="autogenerate", 
        action="store_false", 
        default=True,
        help="Don't auto-generate migration from model changes"
    )
    create_parser.set_defaults(func=create_migration)
    
    # Upgrade command
    upgrade_parser = subparsers.add_parser("upgrade", help="Upgrade database")
    upgrade_parser.add_argument(
        "revision", 
        nargs="?", 
        default="head",
        help="Target revision (default: head)"
    )
    upgrade_parser.set_defaults(func=upgrade_database)
    
    # Downgrade command
    downgrade_parser = subparsers.add_parser("downgrade", help="Downgrade database")
    downgrade_parser.add_argument("revision", help="Target revision")
    downgrade_parser.set_defaults(func=downgrade_database)
    
    # Status command
    status_parser = subparsers.add_parser("status", help="Show migration status")
    status_parser.add_argument(
        "--history", 
        action="store_true",
        help="Show migration history"
    )
    status_parser.set_defaults(func=show_status)
    
    # Init command
    init_parser = subparsers.add_parser("init", help="Initialize database")
    init_parser.add_argument(
        "--no-migrations",
        dest="use_migrations",
        action="store_false",
        default=True,
        help="Don't use migrations, create tables directly"
    )
    init_parser.set_defaults(func=lambda args: asyncio.run(init_database(args)))
    
    # Auto-upgrade command
    auto_parser = subparsers.add_parser("auto-upgrade", help="Auto-upgrade database if needed")
    auto_parser.set_defaults(func=auto_upgrade)
    
    # Parse arguments
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    # Execute command
    args.func(args)


if __name__ == "__main__":
    main()
