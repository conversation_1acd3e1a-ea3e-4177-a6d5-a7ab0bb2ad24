{"openai_compatible_services": {"ollama": {"description": "Local Ollama server", "api_base": "http://localhost:11434/v1", "api_key": "ollama", "models": ["llama2", "codellama", "mistral", "neural-chat"], "setup_instructions": ["1. Install Ollama: curl -fsSL https://ollama.ai/install.sh | sh", "2. Pull a model: ollama pull llama2", "3. Start server: ollama serve"]}, "localai": {"description": "Self-hosted LocalAI", "api_base": "http://localhost:8080/v1", "api_key": "not-needed", "models": ["gpt-3.5-turbo", "gpt-4", "custom-model"], "setup_instructions": ["1. Run with Docker: docker run -p 8080:8080 localai/localai:latest", "2. Or install locally: https://localai.io/basics/getting_started/"]}, "vllm": {"description": "vLLM inference server", "api_base": "http://localhost:8000/v1", "api_key": "token-abc123", "models": ["microsoft/DialoGPT-medium", "meta-llama/Llama-2-7b-chat-hf"], "setup_instructions": ["1. Install vLLM: pip install vllm", "2. Start server: python -m vllm.entrypoints.openai.api_server --model MODEL_NAME"]}, "huggingface": {"description": "Hugging Face Inference Endpoints", "api_base": "https://your-endpoint.huggingface.co/v1", "api_key": "hf_your_token_here", "models": ["depends on your endpoint"], "setup_instructions": ["1. Create an Inference Endpoint on Hugging Face", "2. Get your endpoint URL and token", "3. Use the endpoint URL as api_base"]}, "azure_openai": {"description": "Azure OpenAI Service", "api_base": "https://your-resource.openai.azure.com/", "api_key": "your-azure-openai-key", "models": ["your-deployment-name"], "setup_instructions": ["1. Create Azure OpenAI resource", "2. Deploy a model", "3. Use deployment name as model parameter"]}, "openai": {"description": "Official OpenAI API", "api_base": null, "api_key": "sk-your-openai-key", "models": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"], "setup_instructions": ["1. Get API key from https://platform.openai.com/api-keys", "2. Set OPENAI_API_KEY environment variable"]}}, "example_agent_configs": {"ollama_chat_agent": {"agent_id": "ollama_chat", "agent_type": "openai", "name": "<PERSON><PERSON>ma Chat Agent", "description": "Friendly chat agent using local Ollama", "api_base": "http://localhost:11434/v1", "api_key": "ollama", "model": "llama2", "system_prompt": "You are a helpful and friendly AI assistant running locally. Be conversational and helpful."}, "localai_coding_agent": {"agent_id": "localai_coder", "agent_type": "react", "name": "LocalAI Coding Agent", "description": "Coding assistant using LocalAI", "api_base": "http://localhost:8080/v1", "api_key": "not-needed", "model": "gpt-3.5-turbo", "system_prompt": "You are a coding assistant. Help users with programming questions and provide code examples."}, "vllm_research_agent": {"agent_id": "vllm_researcher", "agent_type": "openai", "name": "vLLM Research Agent", "description": "Research assistant using vLLM", "api_base": "http://localhost:8000/v1", "api_key": "token-abc123", "model": "meta-llama/Llama-2-7b-chat-hf", "system_prompt": "You are a research assistant. Help users find information and analyze topics thoroughly."}}, "curl_examples": {"create_ollama_agent": {"description": "Create an agent using Ollama", "command": "curl -X POST \"http://localhost:8000/agents\" -H \"Content-Type: application/json\" -d '{\"agent_id\": \"ollama_agent\", \"agent_type\": \"openai\", \"name\": \"Ollama Agent\", \"api_base\": \"http://localhost:11434/v1\", \"api_key\": \"ollama\", \"model\": \"llama2\"}'"}, "chat_with_agent": {"description": "Chat with a specific agent", "command": "curl -X POST \"http://localhost:8000/chat\" -H \"Content-Type: application/json\" -d '{\"message\": \"Hello!\", \"agent_id\": \"ollama_agent\"}'"}, "list_agents": {"description": "List all agents", "command": "curl -X GET \"http://localhost:8000/agents\""}}, "troubleshooting": {"connection_refused": {"problem": "Connection refused when creating agent", "solutions": ["Make sure the service is running (e.g., ollama serve)", "Check the API base URL is correct", "Verify the port is not blocked by firewall"]}, "authentication_error": {"problem": "Authentication or API key errors", "solutions": ["Check if the service requires a real API key", "For local services, try any string as API key", "Verify the API key format is correct"]}, "model_not_found": {"problem": "Model not found error", "solutions": ["Check available models in your service", "For Ollama: ollama list", "Make sure the model is downloaded/available"]}}}