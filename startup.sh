#!/bin/bash

# Startup script that supports environment-specific configurations

# Default values
PROJECT_ENV=${PROJECT_ENV:-"qa"}

# Support for -e project_env parameter
for arg in "$@"; do
    if [[ $arg == -e\ project_env=* ]]; then
        PROJECT_ENV="${arg#-e project_env=}"
    fi
done

echo "Starting application with PROJECT_ENV=$PROJECT_ENV"

# Check if environment-specific .env file exists and load it
if [ -f ".env.$PROJECT_ENV" ]; then
    echo "Loading environment variables from .env.$PROJECT_ENV"
    # Filter out comments and empty lines, then export variables
    export $(grep -v '^#' .env.$PROJECT_ENV | grep -v '^$' | xargs)
else
    echo "No environment-specific .env file found for $PROJECT_ENV"
fi

# Start the application
exec uvicorn app.main:app --proxy-headers --host 0.0.0.0 --port 8000 --env-file=".env.$PROJECT_ENV"