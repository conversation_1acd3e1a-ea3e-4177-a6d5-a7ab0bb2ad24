"""
Test script for knowledge base functionality.
"""

import asyncio
import aiohttp
import json
from pathlib import Path


async def test_knowledge_base():
    """Test knowledge base functionality."""
    base_url = "http://localhost:8000"
    
    # Test data
    test_kb_name = "Test Knowledge Base"
    test_kb_description = "A test knowledge base for demonstration"
    test_doc_content = "This is a test document for the knowledge base. It contains some sample text that will be vectorized and stored in ChromaDB."
    
    async with aiohttp.ClientSession() as session:
        try:
            # 1. Register/Login to get token
            print("1. Getting authentication token...")
            login_data = {
                "username": "test_user",
                "password": "test_password"
            }
            
            try:
                async with session.post(f"{base_url}/auth/register", json=login_data) as resp:
                    if resp.status == 400:
                        # User might already exist, try login
                        async with session.post(f"{base_url}/auth/login", json=login_data) as login_resp:
                            if login_resp.status == 200:
                                auth_result = await login_resp.json()
                                token = auth_result["access_token"]
                            else:
                                print("Failed to authenticate")
                                return
                    else:
                        auth_result = await resp.json()
                        token = auth_result["access_token"]
            except Exception as e:
                print(f"Authentication error: {e}")
                # Use a dummy token for testing
                token = "dummy_token"
            
            headers = {"Authorization": f"Bearer {token}"}
            
            # 2. Create knowledge base
            print("2. Creating knowledge base...")
            kb_data = {
                "name": test_kb_name,
                "description": test_kb_description,
                "is_public": False
            }
            
            async with session.post(f"{base_url}/api/knowledge/bases", json=kb_data, headers=headers) as resp:
                if resp.status == 200:
                    kb_result = await resp.json()
                    kb_id = kb_result["kb_id"]
                    print(f"✅ Created knowledge base: {kb_id}")
                else:
                    print(f"Failed to create knowledge base: {resp.status}")
                    return
            
            # 3. List knowledge bases
            print("3. Listing knowledge bases...")
            async with session.get(f"{base_url}/api/knowledge/bases", headers=headers) as resp:
                if resp.status == 200:
                    kbs = await resp.json()
                    print(f"✅ Found {len(kbs)} knowledge bases")
                else:
                    print(f"Failed to list knowledge bases: {resp.status}")
            
            # 4. Create test document
            print("4. Creating test document...")
            test_file_path = Path("test_document.txt")
            with open(test_file_path, "w") as f:
                f.write(test_doc_content)
            
            # Upload document
            form_data = aiohttp.FormData()
            form_data.add_field('file', open(test_file_path, 'rb'), filename='test_document.txt', content_type='text/plain')
            form_data.add_field('metadata', json.dumps({"source": "test", "type": "demo"}))
            
            async with session.post(f"{base_url}/api/knowledge/bases/{kb_id}/documents", data=form_data, headers=headers) as resp:
                if resp.status == 200:
                    doc_result = await resp.json()
                    doc_id = doc_result["doc_id"]
                    print(f"✅ Uploaded document: {doc_id}")
                else:
                    print(f"Failed to upload document: {resp.status}")
                    return
            
            # 5. List documents
            print("5. Listing documents...")
            async with session.get(f"{base_url}/api/knowledge/bases/{kb_id}/documents", headers=headers) as resp:
                if resp.status == 200:
                    docs = await resp.json()
                    print(f"✅ Found {len(docs)} documents")
                else:
                    print(f"Failed to list documents: {resp.status}")
            
            # 6. Get knowledge base stats
            print("6. Getting knowledge base stats...")
            async with session.get(f"{base_url}/api/knowledge/bases/{kb_id}/stats", headers=headers) as resp:
                if resp.status == 200:
                    stats = await resp.json()
                    print(f"✅ Knowledge base stats: {stats}")
                else:
                    print(f"Failed to get stats: {resp.status}")
            
            # 7. Wait a bit for processing and then search
            print("7. Waiting for document processing...")
            await asyncio.sleep(5)
            
            # 8. Search documents
            print("8. Searching documents...")
            search_data = {
                "query": "test document",
                "limit": 5
            }
            
            async with session.post(f"{base_url}/api/knowledge/bases/{kb_id}/search", json=search_data, headers=headers) as resp:
                if resp.status == 200:
                    results = await resp.json()
                    print(f"✅ Found {len(results)} search results")
                    for i, result in enumerate(results[:3]):
                        print(f"   Result {i+1}: {result['content'][:100]}...")
                else:
                    print(f"Failed to search documents: {resp.status}")
            
            # 9. Clean up
            print("9. Cleaning up...")
            test_file_path.unlink(missing_ok=True)
            
            print("\n🎉 Knowledge base test completed successfully!")
            
        except Exception as e:
            print(f"❌ Test failed: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    print("🧪 Testing Knowledge Base Functionality")
    print("=" * 50)
    asyncio.run(test_knowledge_base())