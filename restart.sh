#!/bin/bash

# 重启脚本：激活虚拟环境，停止服务，启动服务

set -e

echo "正在重启 Easy Agent Center..."

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 激活虚拟环境
echo "正在激活虚拟环境..."
if [ -d ".venv" ]; then
    source .venv/bin/activate
elif [ -d "venv" ]; then
    source venv/bin/activate
else
    echo "未找到虚拟环境，尝试使用 poetry..."
    if command -v poetry &> /dev/null; then
        poetry shell
    else
        echo "警告：未找到虚拟环境或poetry，将使用系统Python"
    fi
fi

# 停止服务
echo "正在停止服务..."
if [ -f "stop_server.py" ]; then
    python stop_server.py || echo "服务可能未运行，继续启动..."
elif [ -f "stop_server.sh" ]; then
    bash stop_server.sh || echo "服务可能未运行，继续启动..."
else
    echo "未找到停止脚本，尝试使用pkill..."
    pkill -f "python.*run_server.py" || true
    pkill -f "uvicorn.*main:app" || true
fi

# 等待服务完全停止（增加等待时间）
echo "等待服务完全停止..."
sleep 5

# 再次检查并强制杀死任何剩余进程
if [ -f "pids/server.pid" ]; then
    PID=$(cat pids/server.pid)
    if ps -p $PID > /dev/null 2>&1; then
        echo "强制终止残留进程 $PID"
        kill -9 $PID 2>/dev/null || true
    fi
    rm -f pids/server.pid
fi

# 使用pkill再次确保没有残留进程
pkill -9 -f "python.*run_server.py" > /dev/null 2>&1 || true
pkill -9 -f "uvicorn.*main:app" > /dev/null 2>&1 || true
sleep 2

# 启动服务
echo "正在启动服务..."
if [ -f "run_server.py" ]; then
    python run_server.py
else
    echo "未找到启动脚本，尝试直接启动..."
    uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
fi

echo "服务重启完成！"