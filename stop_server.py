#!/usr/bin/env python3
"""
Easy Agent Center - Server Stop Script

This script stops the background FastAPI server.
"""

import os
import sys
import signal
from pathlib import Path

def main():
    """Stop the FastAPI server."""
    print("🛑 Stopping Easy Agent Center server...")
    
    pid_dir = Path("pids")
    pid_file = pid_dir / "server.pid"

    if not pid_file.exists():
        print("🤷 PID file not found. The server may not be running.")
        print(f"   (Expected location: {pid_file.resolve()})")
        sys.exit(1)

    try:
        with open(pid_file, "r") as f:
            pid = int(f.read().strip())
    except (IOError, ValueError) as e:
        print(f"❌ Error reading PID file: {e}")
        sys.exit(1)

    print(f"🔎 Found PID: {pid}. Attempting to stop the process...")

    try:
        # Send SIGTERM for graceful shutdown
        os.kill(pid, signal.SIGTERM)
        print("✅ Sent SIGTERM signal to process. Waiting for it to terminate...")
        
        # Wait for the process to terminate
        for _ in range(30): # Wait up to 30 seconds (increased from 10)
            try:
                os.kill(pid, 0) # Check if process exists
                print("   - Still running, waiting...")
                import time
                time.sleep(1)
            except OSError:
                # Process does not exist
                print("🎉 Server process terminated successfully.")
                return  # Exit successfully
        
        # If loop finishes without break, process is still running
        print("⚠️ Server did not terminate gracefully within 30 seconds. Sending SIGKILL (force kill)...")
        try:
            os.kill(pid, signal.SIGKILL)
            print("💥 Sent SIGKILL. The process should now be stopped.")
            
            # Wait a bit to ensure the process is killed
            import time
            for _ in range(5):  # Wait up to 5 seconds
                try:
                    os.kill(pid, 0)  # Check if process still exists
                    print("   - Still running, waiting for force kill to take effect...")
                    time.sleep(1)
                except OSError:
                    print("🎉 Server process terminated successfully with SIGKILL.")
                    return  # Exit successfully
            
            print("⚠️ Server still running after SIGKILL. Manual intervention may be required.")
            sys.exit(1)
        except ProcessLookupError:
            print("🎉 Server process was terminated before SIGKILL could be sent.")

    except ProcessLookupError:
        print(f"🤷 Process with PID {pid} not found. It might have already stopped.")
    except Exception as e:
        print(f"❌ An unexpected error occurred: {e}")
        sys.exit(1)
    finally:
        # Clean up the PID file
        if pid_file.exists():
            pid_file.unlink()
            print(f"🗑️ Removed PID file: {pid_file.resolve()}")

if __name__ == "__main__":
    main()
