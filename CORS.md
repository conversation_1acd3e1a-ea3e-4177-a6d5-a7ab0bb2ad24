# CORS Configuration

The Easy Agent Center API supports Cross-Origin Resource Sharing (CORS) to enable web applications from different domains to access the API.

## Configuration

CORS is configured through the `CORS_ORIGINS` environment variable in your `.env` file:

```env
# Allow all origins (default - use with caution in production)
CORS_ORIGINS=*

# Allow specific origins (recommended for production)
CORS_ORIGINS=http://localhost:3000,https://yourdomain.com,https://app.yourdomain.com

# Allow localhost with different ports (useful for development)
CORS_ORIGINS=http://localhost:3000,http://localhost:8080,http://127.0.0.1:3000
```

## Default Settings

- **Allowed Origins**: `*` (all origins) - configurable via `CORS_ORIGINS`
- **Allowed Methods**: `GET`, `POST`, `PUT`, `DELETE`, `OPTIONS`
- **Allowed Headers**: `*` (all headers)
- **Allow Credentials**: `true`

## Security Recommendations

### Development
```env
CORS_ORIGINS=http://localhost:3000,http://localhost:8080
```

### Production
```env
CORS_ORIGINS=https://yourdomain.com,https://app.yourdomain.com
```

### Staging
```env
CORS_ORIGINS=https://staging.yourdomain.com,https://dev.yourdomain.com
```

## Testing CORS

You can test CORS functionality using curl:

```bash
# Test preflight request
curl -X OPTIONS http://localhost:8000/agents \
  -H "Origin: http://localhost:3000" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type"

# Test actual request
curl -X GET http://localhost:8000/agents \
  -H "Origin: http://localhost:3000"
```

## Common Issues

1. **CORS errors in browser**: Make sure your frontend origin is listed in `CORS_ORIGINS`
2. **Credentials not working**: Ensure `allow_credentials=True` is set (default)
3. **Custom headers blocked**: All headers are allowed by default (`allow_headers=["*"]`)

## Environment Variables

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `CORS_ORIGINS` | Comma-separated list of allowed origins | `*` | `http://localhost:3000,https://app.example.com` |